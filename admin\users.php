<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireAdmin();

$page_title = 'Управление пользователями';

// Handle user operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', 'Недействительный токен безопасности');
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_balance') {
            $user_id = intval($_POST['user_id'] ?? 0);
            $amount = floatval($_POST['amount'] ?? 0);
            $operation = $_POST['operation'] ?? 'add';
            $notes = sanitizeInput($_POST['notes'] ?? '');
            
            if ($user_id && $amount > 0) {
                if (updateUserBalance($user_id, $amount, $operation)) {
                    // Log the balance change
                    $db = getDB();
                    $stmt = $db->prepare("
                        INSERT INTO balance_logs (user_id, amount, operation, admin_notes, created_by) 
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$user_id, $amount, $operation, $notes, getCurrentUserId()]);
                    
                    setFlashMessage('success', 'Баланс пользователя успешно обновлен');
                } else {
                    setFlashMessage('error', 'Ошибка при обновлении баланса');
                }
            } else {
                setFlashMessage('error', 'Некорректные данные');
            }
        }
        
        elseif ($action === 'update_status') {
            $user_id = intval($_POST['user_id'] ?? 0);
            $status = $_POST['status'] ?? '';
            $role = $_POST['role'] ?? '';

            if ($user_id && in_array($status, ['active', 'suspended']) && in_array($role, ['user', 'admin'])) {
                // Prevent changing own status/role
                if ($user_id == getCurrentUserId()) {
                    setFlashMessage('error', 'Нельзя изменить собственный статус или роль');
                } else {
                    $db = getDB();
                    $is_admin = ($role === 'admin') ? 1 : 0;
                    $is_suspended = ($status === 'suspended') ? 1 : 0;
                    $stmt = $db->prepare("UPDATE users SET status = ?, role = ?, is_admin = ?, is_suspended = ? WHERE id = ?");
                    if ($stmt->execute([$status, $role, $is_admin, $is_suspended, $user_id])) {
                        setFlashMessage('success', 'Статус и роль пользователя обновлены');
                    } else {
                        setFlashMessage('error', 'Ошибка при обновлении данных пользователя');
                    }
                }
            } else {
                setFlashMessage('error', 'Неверные данные');
            }
        }
        
        elseif ($action === 'delete_user') {
            $user_id = intval($_POST['user_id'] ?? 0);
            if ($user_id) {
                $db = getDB();
                
                // Check if user has active investments
                $stmt = $db->prepare("SELECT COUNT(*) as count FROM user_investments WHERE user_id = ? AND is_active = 1");
                $stmt->execute([$user_id]);
                $active_investments = $stmt->fetch()['count'];
                
                if ($active_investments > 0) {
                    setFlashMessage('error', 'Нельзя удалить пользователя с активными инвестициями');
                } else {
                    try {
                        $db->beginTransaction();
                        
                        // Delete related records
                        $stmt = $db->prepare("DELETE FROM user_investments WHERE user_id = ?");
                        $stmt->execute([$user_id]);
                        
                        $stmt = $db->prepare("DELETE FROM transactions WHERE user_id = ?");
                        $stmt->execute([$user_id]);
                        
                        $stmt = $db->prepare("DELETE FROM balance_logs WHERE user_id = ?");
                        $stmt->execute([$user_id]);
                        
                        // Delete user
                        $stmt = $db->prepare("DELETE FROM users WHERE id = ? AND is_admin = 0");
                        $stmt->execute([$user_id]);
                        
                        $db->commit();
                        setFlashMessage('success', 'Пользователь успешно удален');
                    } catch (Exception $e) {
                        $db->rollback();
                        setFlashMessage('error', 'Ошибка при удалении пользователя: ' . $e->getMessage());
                    }
                }
            }
        }
    }
    
    redirect('users.php');
}

// Get users with statistics
$db = getDB();
$stmt = $db->prepare("
    SELECT u.*, 
           COUNT(DISTINCT ui.id) as total_investments,
           SUM(CASE WHEN ui.is_active = 1 THEN ui.amount ELSE 0 END) as total_invested,
           COUNT(DISTINCT t.id) as total_transactions
    FROM users u
    LEFT JOIN user_investments ui ON u.id = ui.user_id
    LEFT JOIN transactions t ON u.id = t.user_id
    WHERE u.is_admin = 0
    GROUP BY u.id
    ORDER BY u.created_at DESC
");
$stmt->execute();
$users = $stmt->fetchAll();

// Get user for editing if requested
$edit_user = null;
if (isset($_GET['edit'])) {
    $edit_id = intval($_GET['edit']);
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ? AND is_admin = 0");
    $stmt->execute([$edit_id]);
    $edit_user = $stmt->fetch();
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Flash Messages -->
        <?php
        $success_message = getFlashMessage('success');
        $error_message = getFlashMessage('error');
        ?>

        <?php if ($success_message): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg shadow-lg animate-fade-in" id="success-alert">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-3"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                    <button onclick="closeAlert('success-alert')" class="text-green-500 hover:text-green-700 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg shadow-lg animate-fade-in" id="error-alert">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-500 mr-3"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                    <button onclick="closeAlert('error-alert')" class="text-red-500 hover:text-red-700 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Управление пользователями</h1>
                <p class="text-gray-600 mt-2">Просмотр и управление учетными записями пользователей</p>
            </div>
            <a href="index.php" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                <i class="fas fa-arrow-left mr-2"></i>Назад к панели
            </a>
        </div>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Всего пользователей</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo count($users); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-user-check text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Активные</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo count(array_filter($users, function($u) {
                                $status = isset($u['status']) ? $u['status'] : ($u['is_suspended'] ? 'suspended' : 'active');
                                return $status === 'active';
                            })); ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">С инвестициями</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo count(array_filter($users, function($u) { return $u['total_investments'] > 0; })); ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Общий баланс</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo formatCurrency(array_sum(array_column($users, 'balance'))); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users List -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Список пользователей</h2>
            </div>
            <div class="p-6">
                <?php if (empty($users)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-600">Пока нет зарегистрированных пользователей</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Пользователь</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Баланс</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Инвестиции</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Статус</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Регистрация</th>
                                    <th class="text-center py-3 px-4 font-semibold text-gray-700">Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="font-semibold text-gray-800"><?php echo htmlspecialchars($user['username']); ?></p>
                                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($user['email']); ?></p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-lg font-bold text-green-600"><?php echo formatCurrency($user['balance']); ?></span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="text-sm">
                                                <p class="font-semibold"><?php echo $user['total_investments']; ?> инвестиций</p>
                                                <p class="text-gray-600"><?php echo formatCurrency($user['total_invested']); ?> вложено</p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <?php
                                            $user_status = isset($user['status']) ? $user['status'] : ($user['is_suspended'] ? 'suspended' : 'active');
                                            $user_role = isset($user['role']) ? $user['role'] : ($user['is_admin'] ? 'admin' : 'user');
                                            ?>
                                            <div class="space-y-1">
                                                <span class="px-2 py-1 rounded-full text-xs font-semibold <?php
                                                    echo $user_status === 'suspended' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800';
                                                ?>">
                                                    <?php echo $user_status === 'suspended' ? 'Заблокирован' : 'Активен'; ?>
                                                </span>
                                                <br>
                                                <span class="px-2 py-1 rounded-full text-xs font-semibold <?php
                                                    echo $user_role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800';
                                                ?>">
                                                    <?php echo $user_role === 'admin' ? 'Админ' : 'Пользователь'; ?>
                                                </span>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-gray-800"><?php echo timeAgo($user['created_at']); ?></span>
                                        </td>
                                        <td class="py-4 px-4 text-center">
                                            <div class="flex justify-center space-x-1">
                                                <button onclick="openBalanceModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>', <?php echo $user['balance']; ?>)"
                                                        class="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600 transition-colors" title="Изменить баланс">
                                                    <i class="fas fa-wallet"></i>
                                                </button>
                                                <?php
                                                $user_status = isset($user['status']) ? $user['status'] : ($user['is_suspended'] ? 'suspended' : 'active');
                                                $user_role = isset($user['role']) ? $user['role'] : ($user['is_admin'] ? 'admin' : 'user');
                                                ?>
                                                <button onclick="openStatusModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>', '<?php echo $user_status; ?>', '<?php echo $user_role; ?>')"
                                                        class="bg-purple-500 text-white px-2 py-1 rounded text-xs hover:bg-purple-600 transition-colors" title="Изменить статус/роль">
                                                    <i class="fas fa-user-cog"></i>
                                                </button>
                                                <button onclick="viewUserDetails(<?php echo $user['id']; ?>)"
                                                        class="bg-purple-500 text-white px-2 py-1 rounded text-xs hover:bg-purple-600 transition-colors" title="Подробности">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <form method="POST" class="inline" onsubmit="return confirm('Вы уверены, что хотите удалить этого пользователя? Это действие необратимо!')">
                                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                    <input type="hidden" name="action" value="delete_user">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600 transition-colors" title="Удалить">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Balance Update Modal -->
<div id="balanceModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6 border border-gray-100 shadow-2xl">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-900">Изменить баланс</h3>
                <button onclick="closeBalanceModal()" class="text-gray-500 hover:text-gray-700 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div id="balanceModalContent" class="mb-6">
                <!-- Content will be populated by JavaScript -->
            </div>

            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="update_balance">
                <input type="hidden" name="user_id" id="balanceUserId">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Операция</label>
                    <select name="operation" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="add">Пополнить баланс</option>
                        <option value="subtract">Списать с баланса</option>
                    </select>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Сумма ($)</label>
                    <input type="number" name="amount" step="0.01" min="0.01" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Заметки</label>
                    <textarea name="notes" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Причина изменения баланса..."></textarea>
                </div>

                <div class="flex space-x-3">
                    <button type="button" onclick="closeBalanceModal()"
                            class="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                        Отмена
                    </button>
                    <button type="submit" class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-2 rounded-lg font-medium transition-all duration-300 shadow-lg">
                        Применить
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Status/Role Update Modal -->
<div id="statusModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6 border border-gray-100 shadow-2xl">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-900">Изменить статус/роль</h3>
                <button onclick="closeStatusModal()" class="text-gray-500 hover:text-gray-700 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div id="statusModalContent" class="mb-6">
                <!-- Content will be populated by JavaScript -->
            </div>

            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="user_id" id="statusUserId">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Статус</label>
                    <select name="status" id="currentStatus" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="active">Активный</option>
                        <option value="suspended">Заблокирован</option>
                    </select>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Роль</label>
                    <select name="role" id="currentRole" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="user">Пользователь</option>
                        <option value="admin">Администратор</option>
                    </select>
                </div>

                <div class="flex space-x-3">
                    <button type="button" onclick="closeStatusModal()"
                            class="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                        Отмена
                    </button>
                    <button type="submit" name="action" value="update_status" class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-2 rounded-lg font-medium transition-all duration-300 shadow-lg">
                        Применить
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openBalanceModal(userId, username, currentBalance) {
    document.getElementById('balanceUserId').value = userId;
    document.getElementById('balanceModalContent').innerHTML = `
        <div class="text-center">
            <p class="text-gray-600 mb-2">Изменение баланса пользователя:</p>
            <p class="font-semibold text-gray-900">${username}</p>
            <p class="text-sm text-gray-600">Текущий баланс: <span class="font-semibold">${formatCurrency(currentBalance)}</span></p>
        </div>
    `;
    document.getElementById('balanceModal').classList.remove('hidden');
}

function closeBalanceModal() {
    document.getElementById('balanceModal').classList.add('hidden');
}

function viewUserDetails(userId) {
    // This could open a detailed modal or redirect to a user details page
    window.open(`user_details.php?id=${userId}`, '_blank');
}

function formatCurrency(amount) {
    return '$' + parseFloat(amount).toFixed(2);
}

// Status/Role Update Modal
function openStatusModal(userId, username, status, role) {
    document.getElementById('statusUserId').value = userId;
    document.getElementById('statusModalContent').innerHTML = `
        <div class="text-center">
            <p class="text-gray-600 mb-2">Изменение статуса/роли пользователя:</p>
            <p class="font-semibold text-gray-900">${username}</p>
            <p class="text-sm text-gray-600">Текущий статус: <span class="font-semibold">${status === 'active' ? 'Активный' : 'Заблокирован'}</span></p>
            <p class="text-sm text-gray-600">Текущая роль: <span class="font-semibold">${role === 'admin' ? 'Администратор' : 'Пользователь'}</span></p>
        </div>
    `;
    document.getElementById('currentStatus').value = status;
    document.getElementById('currentRole').value = role;
    document.getElementById('statusModal').classList.remove('hidden');
}

function closeStatusModal() {
    document.getElementById('statusModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('balanceModal').addEventListener('click', function(e) {
    if (e.target === this) closeBalanceModal();
});

document.getElementById('statusModal').addEventListener('click', function(e) {
    if (e.target === this) closeStatusModal();
});

// Alert functions
function closeAlert(alertId) {
    const alert = document.getElementById(alertId);
    if (alert) {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            alert.remove();
        }, 300);
    }
}

// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('[id$="-alert"]');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                closeAlert(alert.id);
            }
        }, 5000);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
