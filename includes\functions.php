<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/config.php';

// User functions
function getUserById($id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

function getUserByEmail($email) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    return $stmt->fetch();
}

function createUser($username, $email, $password) {
    $db = getDB();
    $password_hash = password_hash($password, PASSWORD_DEFAULT);
    
    $stmt = $db->prepare("INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)");
    return $stmt->execute([$username, $email, $password_hash]);
}

function updateUserBalance($user_id, $amount, $operation = 'add') {
    $db = getDB();
    
    if ($operation === 'add') {
        $stmt = $db->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
    } else {
        $stmt = $db->prepare("UPDATE users SET balance = balance - ? WHERE id = ?");
    }
    
    return $stmt->execute([$amount, $user_id]);
}

// Investment functions
function getAllInvestments($active_only = true) {
    $db = getDB();
    $sql = "SELECT * FROM investments";
    if ($active_only) {
        $sql .= " WHERE is_active = 1";
    }
    $sql .= " ORDER BY created_at DESC";

    $stmt = $db->prepare($sql);
    $stmt->execute();
    $investments = $stmt->fetchAll();

    // Add compatibility fields for each investment
    foreach ($investments as &$investment) {
        $investment = addInvestmentCompatibilityFields($investment);
    }

    return $investments;
}

function getInvestmentById($id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM investments WHERE id = ?");
    $stmt->execute([$id]);
    $investment = $stmt->fetch();

    // Add compatibility fields for old code
    if ($investment) {
        $investment = addInvestmentCompatibilityFields($investment);
    }

    return $investment;
}

// Add compatibility fields for investments to work with old frontend code
function addInvestmentCompatibilityFields($investment) {
    if (!$investment) return $investment;

    // Add missing fields with default values or computed values
    $investment['price'] = $investment['max_amount'] ?? $investment['min_amount'];
    $investment['monthly_rate_min'] = $investment['monthly_rate'];
    $investment['monthly_rate_max'] = $investment['monthly_rate'];
    $investment['return_period_months'] = $investment['duration_months'] ?? 12;

    // Ensure required fields exist with defaults
    if (!isset($investment['location'])) {
        $investment['location'] = ucfirst(str_replace('_', ' ', $investment['category']));
    }
    if (!isset($investment['capital_return'])) {
        $investment['capital_return'] = true;
    }
    if (!isset($investment['features'])) {
        $investment['features'] = '';
    }

    return $investment;
}

function createUserInvestment($user_id, $investment_id, $amount, $monthly_rate, $return_period_months = null) {
    $db = getDB();

    // Get investment details to determine duration if not provided
    if ($return_period_months === null) {
        $investment = getInvestmentById($investment_id);
        $return_period_months = $investment['duration_months'] ?? 12;
    }

    $start_date = date('Y-m-d');
    $end_date = date('Y-m-d', strtotime("+{$return_period_months} months"));

    $stmt = $db->prepare("
        INSERT INTO user_investments (user_id, investment_id, amount, monthly_rate, start_date, end_date)
        VALUES (?, ?, ?, ?, ?, ?)
    ");

    return $stmt->execute([$user_id, $investment_id, $amount, $monthly_rate, $start_date, $end_date]);
}

function getUserInvestments($user_id) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT ui.*, i.title, i.category, i.image_url 
        FROM user_investments ui 
        JOIN investments i ON ui.investment_id = i.id 
        WHERE ui.user_id = ? 
        ORDER BY ui.created_at DESC
    ");
    $stmt->execute([$user_id]);
    return $stmt->fetchAll();
}

// Transaction functions
function createTransaction($user_id, $type, $amount, $screenshot_path = null, $wallet_address = null) {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO transactions (user_id, type, amount, screenshot_path, wallet_address, status)
        VALUES (?, ?, ?, ?, ?, 'pending')
    ");

    return $stmt->execute([$user_id, $type, $amount, $screenshot_path, $wallet_address]);
}

function getUserTransactions($user_id, $limit = 10) {
    $db = getDB();

    // Ensure limit is an integer and within reasonable bounds
    $limit = max(1, min(100, intval($limit)));

    // Get regular transactions and daily profits combined
    $stmt = $db->prepare("
        (SELECT
            id,
            'transaction' as source_type,
            type,
            amount,
            status,
            created_at,
            wallet_address,
            screenshot_path,
            admin_notes
        FROM transactions
        WHERE user_id = ?)
        UNION ALL
        (SELECT
            dp.id,
            'profit' as source_type,
            'profit_credit' as type,
            dp.profit_amount as amount,
            'completed' as status,
            dp.created_at,
            NULL as wallet_address,
            NULL as screenshot_path,
            CONCAT('Daily profit from investment #', ui.investment_id) as admin_notes
        FROM daily_profits dp
        JOIN user_investments ui ON dp.user_investment_id = ui.id
        WHERE ui.user_id = ?)
        ORDER BY created_at DESC
        LIMIT " . $limit
    );
    $stmt->execute([$user_id, $user_id]);
    return $stmt->fetchAll();
}

function getPendingTransactions($type = null) {
    $db = getDB();
    $sql = "
        SELECT t.*, u.username, u.email 
        FROM transactions t 
        JOIN users u ON t.user_id = u.id 
        WHERE t.status = 'pending'
    ";
    
    if ($type) {
        $sql .= " AND t.type = ?";
        $stmt = $db->prepare($sql . " ORDER BY t.created_at ASC");
        $stmt->execute([$type]);
    } else {
        $stmt = $db->prepare($sql . " ORDER BY t.created_at ASC");
        $stmt->execute();
    }
    
    return $stmt->fetchAll();
}

function updateTransactionStatus($transaction_id, $status, $admin_notes = null) {
    $db = getDB();
    $stmt = $db->prepare("
        UPDATE transactions 
        SET status = ?, admin_notes = ?, processed_at = NOW() 
        WHERE id = ?
    ");
    
    return $stmt->execute([$status, $admin_notes, $transaction_id]);
}

// Daily profit calculation
function calculateDailyProfit($user_investment_id, $amount, $monthly_rate) {
    $daily_rate = $monthly_rate / 30;
    return ($amount * $daily_rate) / 100;
}

function addDailyProfit($user_investment_id, $profit_amount, $date = null) {
    $db = getDB();
    
    if (!$date) {
        $date = date('Y-m-d');
    }
    
    $stmt = $db->prepare("
        INSERT IGNORE INTO daily_profits (user_investment_id, profit_amount, profit_date) 
        VALUES (?, ?, ?)
    ");
    
    return $stmt->execute([$user_investment_id, $profit_amount, $date]);
}

// Blog functions
function getBlogPosts($published_only = true, $limit = null) {
    $db = getDB();
    $sql = "SELECT * FROM blog_posts";
    
    if ($published_only) {
        $sql .= " WHERE is_published = 1";
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}

function getBlogPostBySlug($slug) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM blog_posts WHERE slug = ? AND is_published = 1");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

// Contact functions
function saveContactMessage($name, $email, $subject, $message) {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO contact_messages (name, email, subject, message) 
        VALUES (?, ?, ?, ?)
    ");
    
    return $stmt->execute([$name, $email, $subject, $message]);
}

// Statistics functions
function getDashboardStats($user_id) {
    $db = getDB();

    // Get user balance
    $user = getUserById($user_id);
    $balance = $user['balance'] ?? 0;

    // Get total invested amount
    $stmt = $db->prepare("SELECT SUM(amount) as total_invested FROM user_investments WHERE user_id = ? AND is_active = 1");
    $stmt->execute([$user_id]);
    $total_invested = $stmt->fetch()['total_invested'] ?? 0;

    // Get total profit earned
    $stmt = $db->prepare("
        SELECT SUM(dp.profit_amount) as total_profit
        FROM daily_profits dp
        JOIN user_investments ui ON dp.user_investment_id = ui.id
        WHERE ui.user_id = ?
    ");
    $stmt->execute([$user_id]);
    $total_profit = $stmt->fetch()['total_profit'] ?? 0;

    // Get active investments count
    $stmt = $db->prepare("SELECT COUNT(*) as active_investments FROM user_investments WHERE user_id = ? AND is_active = 1");
    $stmt->execute([$user_id]);
    $active_investments = $stmt->fetch()['active_investments'] ?? 0;

    return [
        'balance' => floatval($balance),
        'total_invested' => floatval($total_invested),
        'total_profit' => floatval($total_profit),
        'active_investments' => intval($active_investments)
    ];
}

// Withdrawal processing function

function processWithdrawalRequest($user_id, $amount, $wallet_address) {
    $user = getUserById($user_id);
    $balance = $user['balance'] ?? 0;

    if ($amount < 10) {
        return ['success' => false, 'message' => 'Minimum withdrawal amount is $10'];
    }

    if ($amount > $balance) {
        return ['success' => false, 'message' => 'Insufficient balance'];
    }

    if (empty($wallet_address)) {
        return ['success' => false, 'message' => 'Wallet address is required'];
    }

    // Create withdrawal transaction directly with new schema
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO transactions (user_id, type, amount, wallet_address, status)
        VALUES (?, 'withdrawal', ?, ?, 'pending')
    ");

    if ($stmt->execute([$user_id, $amount, $wallet_address])) {
        return ['success' => true, 'message' => 'Withdrawal request submitted successfully. It will be processed within 24-48 hours.'];
    } else {
        return ['success' => false, 'message' => 'Failed to submit withdrawal request. Please try again.'];
    }
}

// Formatting functions
function formatCurrency($amount) {
    return '$' . number_format(floatval($amount), 2);
}

function formatPercentage($percentage) {
    return number_format(floatval($percentage), 2);
}

// CSRF Protection functions
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Flash message functions
function setFlashMessage($type, $message) {
    $_SESSION['flash_messages'][$type] = $message;
}

function getFlashMessage($type) {
    if (isset($_SESSION['flash_messages'][$type])) {
        $message = $_SESSION['flash_messages'][$type];
        unset($_SESSION['flash_messages'][$type]);
        return $message;
    }
    return null;
}

// Redirect function
function redirect($url) {
    header("Location: $url");
    exit();
}

// File upload functions
function uploadTransactionScreenshot($file) {
    $uploadDir = 'uploads/transactions/';
    $adminUploadDir = 'admin/uploads/transactions/';
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    // Validate file
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'message' => 'No file uploaded'];
    }

    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => 'File size exceeds 5MB limit'];
    }

    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, and GIF are allowed'];
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid('transaction_') . '.' . $extension;
    $filepath = $uploadDir . $filename;
    $adminFilepath = $adminUploadDir . $filename;

    // Create directories if they don't exist
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    if (!file_exists($adminUploadDir)) {
        mkdir($adminUploadDir, 0755, true);
    }

    // Move uploaded file to main location
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        // Copy to admin location for easy access
        if (copy($filepath, $adminFilepath)) {
            return ['success' => true, 'filepath' => $filepath, 'admin_filepath' => $adminFilepath];
        } else {
            // Main upload succeeded, admin copy failed - still return success
            return ['success' => true, 'filepath' => $filepath, 'admin_copy_failed' => true];
        }
    } else {
        return ['success' => false, 'message' => 'Failed to save uploaded file'];
    }
}

// Process deposit request with screenshot
function processDepositRequest($user_id, $amount, $screenshot_file) {
    if ($amount < 10) {
        return ['success' => false, 'message' => 'Minimum deposit amount is $10'];
    }

    // Upload screenshot
    $uploadResult = uploadTransactionScreenshot($screenshot_file);
    if (!$uploadResult['success']) {
        return $uploadResult;
    }

    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO transactions (user_id, type, amount, screenshot_path, status)
        VALUES (?, 'deposit', ?, ?, 'pending')
    ");

    if ($stmt->execute([$user_id, $amount, $uploadResult['filepath']])) {
        return ['success' => true, 'message' => 'Deposit request submitted successfully. Please wait for admin approval.'];
    } else {
        // Delete uploaded file if database insert fails
        if (file_exists($uploadResult['filepath'])) {
            unlink($uploadResult['filepath']);
        }
        return ['success' => false, 'message' => 'Failed to submit deposit request. Please try again.'];
    }
}

// Site settings functions
function getSiteSetting($key, $default = '') {
    $db = getDB();
    $stmt = $db->prepare("SELECT setting_value FROM site_settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    return $result ? $result['setting_value'] : $default;
}

function updateSiteSetting($key, $value, $type = 'text') {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO site_settings (setting_key, setting_value, setting_type)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = CURRENT_TIMESTAMP
    ");
    return $stmt->execute([$key, $value, $type, $value]);
}

function getAllSiteSettings() {
    $db = getDB();
    $stmt = $db->query("SELECT * FROM site_settings ORDER BY setting_key");
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    return $settings;
}
?>
