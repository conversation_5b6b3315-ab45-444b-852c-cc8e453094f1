<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'testuser';
    $_SESSION['email'] = '<EMAIL>';
}

$user_id = $_SESSION['user_id'];

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Тест унифицированной системы хранения</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>🧪 Тест унифицированной системы хранения файлов</h1>";

$all_tests_passed = true;

// Handle test upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'test_unified_upload') {
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-2xl font-bold text-gray-900 mb-4'>📤 Результат тестовой загрузки</h2>";
    
    if (verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $amount = floatval($_POST['amount'] ?? 0);
        $screenshot_file = $_FILES['unified_test_screenshot'] ?? null;
        
        $result = processDepositRequest($user_id, $amount, $screenshot_file);
        
        if ($result['success']) {
            echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4 mb-4'>
                    <h4 class='font-bold text-green-900'><i class='fas fa-check mr-2'></i>Загрузка успешна!</h4>
                    <p class='text-green-700'>" . htmlspecialchars($result['message']) . "</p>
                  </div>";
            
            // Get the latest transaction to verify unified storage
            try {
                $db = getDB();
                $stmt = $db->prepare("SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
                $stmt->execute([$user_id]);
                $latest_transaction = $stmt->fetch();
                
                if ($latest_transaction && !empty($latest_transaction['screenshot_path'])) {
                    $screenshot_path = $latest_transaction['screenshot_path'];
                    
                    echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4'>
                            <h4 class='font-bold text-blue-900'><i class='fas fa-info mr-2'></i>Проверка унифицированного хранения:</h4>";
                    
                    // Verify file is in unified location
                    if (strpos($screenshot_path, 'uploads/transactions/') === 0) {
                        echo "<p class='text-green-700'><i class='fas fa-check mr-1'></i>Файл сохранен в унифицированной директории: $screenshot_path</p>";
                        
                        if (file_exists($screenshot_path)) {
                            echo "<p class='text-green-700'><i class='fas fa-check mr-1'></i>Файл физически существует</p>";
                            
                            // Test admin access
                            $admin_path = 'admin/../' . $screenshot_path;
                            if (file_exists($admin_path)) {
                                echo "<p class='text-green-700'><i class='fas fa-check mr-1'></i>Файл доступен из админ-панели</p>";
                            } else {
                                echo "<p class='text-red-700'><i class='fas fa-times mr-1'></i>Файл НЕ доступен из админ-панели</p>";
                                $all_tests_passed = false;
                            }
                            
                            // Verify no duplicate in admin directory
                            $admin_duplicate = 'admin/uploads/transactions/' . basename($screenshot_path);
                            if (!file_exists($admin_duplicate)) {
                                echo "<p class='text-green-700'><i class='fas fa-check mr-1'></i>Нет дубликата в админ-директории (правильно)</p>";
                            } else {
                                echo "<p class='text-yellow-700'><i class='fas fa-exclamation-triangle mr-1'></i>Обнаружен дубликат в админ-директории</p>";
                            }
                        } else {
                            echo "<p class='text-red-700'><i class='fas fa-times mr-1'></i>Файл НЕ существует физически</p>";
                            $all_tests_passed = false;
                        }
                    } else {
                        echo "<p class='text-red-700'><i class='fas fa-times mr-1'></i>Файл НЕ в унифицированной директории: $screenshot_path</p>";
                        $all_tests_passed = false;
                    }
                    
                    echo "</div>";
                    
                    // Display image if accessible
                    if (file_exists($screenshot_path)) {
                        echo "<div class='bg-gray-50 border border-gray-200 rounded-lg p-4'>
                                <h4 class='font-bold text-gray-900 mb-2'>Предварительный просмотр:</h4>
                                <img src='$screenshot_path' alt='Uploaded screenshot' class='max-w-md rounded-lg shadow-lg border'>
                              </div>";
                    }
                }
            } catch (Exception $e) {
                echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>
                        <h4 class='font-bold text-red-900'><i class='fas fa-times mr-2'></i>Ошибка БД</h4>
                        <p class='text-red-700'>" . $e->getMessage() . "</p>
                      </div>";
                $all_tests_passed = false;
            }
        } else {
            echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>
                    <h4 class='font-bold text-red-900'><i class='fas fa-times mr-2'></i>Ошибка загрузки</h4>
                    <p class='text-red-700'>" . htmlspecialchars($result['message']) . "</p>
                  </div>";
            $all_tests_passed = false;
        }
    } else {
        echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>
                <h4 class='font-bold text-red-900'><i class='fas fa-times mr-2'></i>Ошибка CSRF</h4>
                <p class='text-red-700'>Недействительный токен безопасности</p>
              </div>";
        $all_tests_passed = false;
    }
    
    echo "</div>";
}

// System status check
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🔍 Статус унифицированной системы</h2>";

$system_checks = [
    'Унифицированная директория существует' => is_dir('uploads/transactions/'),
    'Унифицированная директория доступна для записи' => is_writable('uploads/transactions/'),
    'Дублирующая админ-директория отсутствует' => !is_dir('admin/uploads/transactions/'),
    'Функция uploadTransactionScreenshot доступна' => function_exists('uploadTransactionScreenshot'),
    'Функция processDepositRequest доступна' => function_exists('processDepositRequest')
];

foreach ($system_checks as $check => $result) {
    $color = $result ? 'green' : 'red';
    $icon = $result ? 'check' : 'times';
    echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
    
    if (!$result) {
        $all_tests_passed = false;
    }
}

// Check function source for admin directory references
if (function_exists('uploadTransactionScreenshot')) {
    $function_reflection = new ReflectionFunction('uploadTransactionScreenshot');
    $function_file = $function_reflection->getFileName();
    $function_source = file_get_contents($function_file);
    
    if (strpos($function_source, 'admin/uploads/transactions') === false) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Функция загрузки очищена от ссылок на админ-директорию</p>";
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Функция загрузки все еще содержит ссылки на админ-директорию</p>";
        $all_tests_passed = false;
    }
}

echo "</div>";

// Display files in unified directory
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📁 Файлы в унифицированной директории</h2>";

$unified_dir = 'uploads/transactions/';
if (is_dir($unified_dir)) {
    $files = glob($unified_dir . '*');
    
    if (empty($files)) {
        echo "<p class='text-gray-500'>Нет файлов в директории</p>";
    } else {
        echo "<div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>";
        foreach (array_slice($files, 0, 9) as $file) {
            $filename = basename($file);
            $filesize = number_format(filesize($file) / 1024, 2);
            $is_image = in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']);
            
            echo "<div class='border rounded-lg p-3'>
                    <div class='flex items-center mb-2'>
                        <i class='fas fa-" . ($is_image ? 'image' : 'file') . " text-blue-500 mr-2'></i>
                        <span class='text-sm font-medium text-gray-700 truncate'>$filename</span>
                    </div>
                    <p class='text-xs text-gray-500'>Размер: {$filesize} KB</p>";
            
            if ($is_image) {
                echo "<img src='$file' alt='$filename' class='mt-2 w-full h-20 object-cover rounded border'>";
            }
            
            echo "</div>";
        }
        echo "</div>";
        
        if (count($files) > 9) {
            echo "<p class='text-gray-500 text-center mt-4'>... и еще " . (count($files) - 9) . " файлов</p>";
        }
    }
} else {
    echo "<p class='text-red-500'>Унифицированная директория не существует!</p>";
    $all_tests_passed = false;
}

echo "</div>";

// Test upload form
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📤 Тестовая форма унифицированной загрузки</h2>
        <form method='POST' enctype='multipart/form-data' class='space-y-4'>
            <input type='hidden' name='csrf_token' value='" . generateCSRFToken() . "'>
            <input type='hidden' name='action' value='test_unified_upload'>
            
            <div>
                <label class='block text-sm font-medium text-gray-700 mb-2'>Сумма депозита (USDT)</label>
                <input type='number' name='amount' step='0.01' min='10' required value='25'
                       class='w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'>
            </div>
            
            <div>
                <label class='block text-sm font-medium text-gray-700 mb-2'>Скриншот транзакции</label>
                <input type='file' name='unified_test_screenshot' accept='image/*' required
                       class='block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'>
                <p class='text-sm text-gray-500 mt-1'>Загрузите изображение для тестирования унифицированной системы</p>
            </div>
            
            <button type='submit' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-upload mr-2'></i>Тест унифицированной загрузки
            </button>
        </form>
      </div>";

// Final status
if ($all_tests_passed) {
    echo "<div class='bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl p-8 text-white text-center'>
            <div class='text-6xl mb-4'>
                <i class='fas fa-check-circle'></i>
            </div>
            <h2 class='text-3xl font-bold mb-4'>✅ УНИФИЦИРОВАННАЯ СИСТЕМА РАБОТАЕТ!</h2>
            <p class='text-xl mb-6 opacity-90'>Все файлы транзакций хранятся в единой директории uploads/transactions/</p>
            
            <div class='flex flex-wrap justify-center gap-4'>
                <a href='pages/dashboard.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-tachometer-alt mr-2'></i>Dashboard
                </a>
                <a href='admin/transactions.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-cog mr-2'></i>Админ-панель
                </a>
                <a href='unify_file_storage.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-history mr-2'></i>История унификации
                </a>
            </div>
          </div>";
} else {
    echo "<div class='bg-red-50 border-2 border-red-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-red-500 mb-4'>
                <i class='fas fa-exclamation-triangle'></i>
            </div>
            <h2 class='text-3xl font-bold text-red-900 mb-4'>⚠️ Требуется дополнительная работа</h2>
            <p class='text-red-700 text-lg mb-6'>Некоторые тесты унифицированной системы не прошли</p>
            
            <div class='flex flex-wrap justify-center gap-4'>
                <a href='unify_file_storage.php' class='bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-redo mr-2'></i>Повторить унификацию
                </a>
                <a href='diagnose_paths.php' class='bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-search mr-2'></i>Диагностика
                </a>
            </div>
          </div>";
}

echo "</div></body></html>";
?>
