<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Финальная проверка - Poseidon</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-5xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">🎯 Финальная проверка платформы Poseidon</h1>
            
            <?php
            // Start session
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            
            // Include required files
            require_once 'config/database.php';
            require_once 'includes/functions.php';
            
            $allTests = [];
            $criticalPassed = true;
            
            // Test 1: Critical Functions
            $criticalFunctions = ['formatCurrency', 'formatPercentage', 'generateCSRFToken', 'verifyCSRFToken', 'setFlashMessage', 'getFlashMessage', 'redirect'];
            $missingCritical = [];
            
            foreach ($criticalFunctions as $func) {
                if (!function_exists($func)) {
                    $missingCritical[] = $func;
                }
            }
            
            if (empty($missingCritical)) {
                $allTests[] = ['name' => 'Критические функции', 'status' => 'pass', 'message' => 'Все критические функции доступны'];
            } else {
                $allTests[] = ['name' => 'Критические функции', 'status' => 'fail', 'message' => 'Отсутствуют: ' . implode(', ', $missingCritical)];
                $criticalPassed = false;
            }
            
            // Test 2: Database Connection
            try {
                $db = getDB();
                $allTests[] = ['name' => 'Подключение к БД', 'status' => 'pass', 'message' => 'Успешное подключение'];
            } catch (Exception $e) {
                $allTests[] = ['name' => 'Подключение к БД', 'status' => 'fail', 'message' => 'Ошибка: ' . $e->getMessage()];
                $criticalPassed = false;
            }
            
            // Test 3: Investments Table
            try {
                $db = getDB();
                $stmt = $db->query("SELECT COUNT(*) as count FROM investments");
                $result = $stmt->fetch();
                if ($result['count'] > 0) {
                    $allTests[] = ['name' => 'Таблица инвестиций', 'status' => 'pass', 'message' => $result['count'] . ' инвестиций найдено'];
                } else {
                    $allTests[] = ['name' => 'Таблица инвестиций', 'status' => 'warning', 'message' => 'Таблица пуста'];
                }
            } catch (Exception $e) {
                $allTests[] = ['name' => 'Таблица инвестиций', 'status' => 'fail', 'message' => 'Ошибка: ' . $e->getMessage()];
                $criticalPassed = false;
            }
            
            // Test 4: Function Calls
            try {
                $testCurrency = formatCurrency(1000);
                $testPercent = formatPercentage(10.5);
                $testToken = generateCSRFToken();
                
                if (!empty($testCurrency) && !empty($testPercent) && !empty($testToken)) {
                    $allTests[] = ['name' => 'Вызовы функций', 'status' => 'pass', 'message' => 'Все функции работают корректно'];
                } else {
                    $allTests[] = ['name' => 'Вызовы функций', 'status' => 'warning', 'message' => 'Функции работают с предупреждениями'];
                }
            } catch (Exception $e) {
                $allTests[] = ['name' => 'Вызовы функций', 'status' => 'fail', 'message' => 'Ошибка: ' . $e->getMessage()];
                $criticalPassed = false;
            }
            
            // Test 5: Page Accessibility
            $pages = [
                'check_errors.php' => 'Проверка ошибок',
                'test_system.php' => 'Системный тест',
                'test_redirect.php' => 'Тест redirect',
                'database/update_via_web.php' => 'Обновление БД',
                'admin/investments.php' => 'Админ-панель',
                'pages/dashboard.php' => 'Дашборд'
            ];
            
            $accessiblePages = 0;
            foreach ($pages as $page => $name) {
                if (file_exists($page)) {
                    $accessiblePages++;
                }
            }
            
            if ($accessiblePages === count($pages)) {
                $allTests[] = ['name' => 'Доступность страниц', 'status' => 'pass', 'message' => 'Все страницы доступны'];
            } else {
                $allTests[] = ['name' => 'Доступность страниц', 'status' => 'warning', 'message' => $accessiblePages . '/' . count($pages) . ' страниц доступны'];
            }
            
            // Display results
            echo '<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">';
            foreach ($allTests as $test) {
                $bgColor = $test['status'] === 'pass' ? 'bg-green-50 border-green-200' : 
                          ($test['status'] === 'warning' ? 'bg-yellow-50 border-yellow-200' : 'bg-red-50 border-red-200');
                $textColor = $test['status'] === 'pass' ? 'text-green-800' : 
                            ($test['status'] === 'warning' ? 'text-yellow-800' : 'text-red-800');
                $icon = $test['status'] === 'pass' ? '✅' : 
                       ($test['status'] === 'warning' ? '⚠️' : '❌');
                
                echo '<div class="' . $bgColor . ' border rounded-lg p-4">';
                echo '<div class="flex items-center justify-between mb-2">';
                echo '<h3 class="font-semibold ' . $textColor . '">' . $icon . ' ' . $test['name'] . '</h3>';
                echo '<span class="text-xs ' . $textColor . ' uppercase font-medium">' . $test['status'] . '</span>';
                echo '</div>';
                echo '<p class="text-sm ' . $textColor . '">' . $test['message'] . '</p>';
                echo '</div>';
            }
            echo '</div>';
            
            // Overall Status
            echo '<div class="mb-8 p-6 rounded-lg border-2 ' . ($criticalPassed ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300') . '">';
            echo '<h2 class="text-2xl font-bold ' . ($criticalPassed ? 'text-green-900' : 'text-red-900') . ' mb-3">';
            echo $criticalPassed ? '🎉 Платформа Poseidon готова к работе!' : '⚠️ Обнаружены критические проблемы';
            echo '</h2>';
            echo '<p class="' . ($criticalPassed ? 'text-green-800' : 'text-red-800') . ' text-lg">';
            if ($criticalPassed) {
                echo 'Все критические ошибки исправлены! Функция redirect() и другие компоненты работают корректно.';
            } else {
                echo 'Пожалуйста, исправьте обнаруженные проблемы перед использованием платформы.';
            }
            echo '</p>';
            echo '</div>';
            ?>
            
            <!-- Quick Links -->
            <div class="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-9 gap-1 mb-6">
                <a href="check_errors.php" class="bg-blue-500 hover:bg-blue-600 text-white px-1 py-2 rounded text-center text-xs font-medium transition-colors">
                    Ошибки
                </a>
                <a href="test_system.php" class="bg-purple-500 hover:bg-purple-600 text-white px-1 py-2 rounded text-center text-xs font-medium transition-colors">
                    Система
                </a>
                <a href="test_redirect.php" class="bg-orange-500 hover:bg-orange-600 text-white px-1 py-2 rounded text-center text-xs font-medium transition-colors">
                    Redirect
                </a>
                <a href="test_foreign_keys.php" class="bg-cyan-500 hover:bg-cyan-600 text-white px-1 py-2 rounded text-center text-xs font-medium transition-colors">
                    FK
                </a>
                <a href="test_data_compatibility.php" class="bg-pink-500 hover:bg-pink-600 text-white px-1 py-2 rounded text-center text-xs font-medium transition-colors">
                    Данные
                </a>
                <a href="test_csrf_functions.php" class="bg-yellow-500 hover:bg-yellow-600 text-white px-1 py-2 rounded text-center text-xs font-medium transition-colors">
                    CSRF
                </a>
                <a href="database/update_via_web.php" class="bg-indigo-500 hover:bg-indigo-600 text-white px-1 py-2 rounded text-center text-xs font-medium transition-colors">
                    БД
                </a>
                <a href="admin/investments.php" class="bg-green-500 hover:bg-green-600 text-white px-1 py-2 rounded text-center text-xs font-medium transition-colors">
                    Админ
                </a>
                <a href="pages/dashboard.php" class="bg-red-500 hover:bg-red-600 text-white px-1 py-2 rounded text-center text-xs font-medium transition-colors">
                    Дашборд
                </a>
            </div>
            
            <!-- Summary -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-900 mb-3">📋 Резюме исправлений</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                    <div>
                        <h4 class="font-semibold mb-2">✅ Исправленные ошибки:</h4>
                        <ul class="space-y-1">
                            <li>• Fatal Error "Cannot redeclare redirect()"</li>
                            <li>• Дублирование функций formatCurrency, formatPercentage</li>
                            <li>• Дублирование CSRF функций</li>
                            <li>• Ошибки внешних ключей БД</li>
                            <li>• Foreign key constraint violations</li>
                            <li>• "Undefined array key" warnings</li>
                            <li>• <strong>НОВОЕ:</strong> "Call to undefined function validateCSRFToken()"</li>
                            <li>• Несовместимость имен CSRF функций</li>
                            <li>• Несовместимость структуры БД и фронтенда</li>
                            <li>• Отсутствующие поля данных</li>
                            <li>• Проблемы с session_start()</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">🔧 Улучшения:</h4>
                        <ul class="space-y-1">
                            <li>• Английский интерфейс в дашборде</li>
                            <li>• Система тестирования</li>
                            <li>• Безопасное обновление БД с FK</li>
                            <li>• Тестирование внешних ключей</li>
                            <li>• Совместимость данных БД</li>
                            <li>• <strong>НОВОЕ:</strong> Тестирование CSRF функций</li>
                            <li>• Исправление имен функций</li>
                            <li>• Автоматическое добавление полей</li>
                            <li>• Проверка ошибок</li>
                            <li>• Тестирование redirect</li>
                            <li>• Целостность данных</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
