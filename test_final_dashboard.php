<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'testuser';
    $_SESSION['email'] = '<EMAIL>';
}

$user_id = $_SESSION['user_id'];

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Финальный тест Dashboard</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>🎯 Финальный тест Dashboard</h1>";

$all_tests_passed = true;

// Test 1: Check if dashboard.php has been fixed
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📄 Тест 1: Проверка исправлений в Dashboard.php</h2>";

$dashboard_file = 'pages/dashboard.php';
if (file_exists($dashboard_file)) {
    $dashboard_content = file_get_contents($dashboard_file);
    
    $fixes_check = [
        'Убран редирект после успешной загрузки' => strpos($dashboard_content, "redirect('dashboard.php');") === false,
        'Используются переменные сообщений' => strpos($dashboard_content, '$success_message = $result[\'message\'];') !== false,
        'Нет flash messages для депозита' => strpos($dashboard_content, "setFlashMessage('success'") === false,
        'Валидация формы присутствует' => strpos($dashboard_content, 'validateDepositForm') !== false,
        'Модальное окно использует style.display' => strpos($dashboard_content, 'style="display: none;"') !== false,
        'Расширенная отладка присутствует' => strpos($dashboard_content, 'File upload debug - Name:') !== false
    ];
    
    foreach ($fixes_check as $check => $result) {
        $color = $result ? 'green' : 'red';
        $icon = $result ? 'check' : 'times';
        echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
        
        if (!$result) {
            $all_tests_passed = false;
        }
    }
} else {
    echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Файл dashboard.php не найден</p>";
    $all_tests_passed = false;
}

echo "</div>";

// Test 2: Simulate file upload
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📤 Тест 2: Симуляция загрузки файла</h2>";

// Create a test image
$test_image_data = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
$test_file_path = 'test_final_upload.png';
file_put_contents($test_file_path, $test_image_data);

$test_file = [
    'name' => 'test_final_upload.png',
    'type' => 'image/png',
    'size' => strlen($test_image_data),
    'tmp_name' => $test_file_path,
    'error' => 0
];

// Test the complete flow
if (function_exists('processDepositRequest')) {
    $result = processDepositRequest($user_id, 50.00, $test_file);
    
    if ($result['success']) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>processDepositRequest работает корректно</p>";
        
        // Check if file was saved
        try {
            $db = getDB();
            $stmt = $db->prepare("SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
            $stmt->execute([$user_id]);
            $latest_transaction = $stmt->fetch();
            
            if ($latest_transaction && !empty($latest_transaction['screenshot_path'])) {
                $screenshot_path = $latest_transaction['screenshot_path'];
                echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Файл сохранен в БД: $screenshot_path</p>";
                
                if (file_exists($screenshot_path)) {
                    echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Файл существует физически</p>";
                    
                    // Test admin access
                    $admin_path = 'admin/../' . $screenshot_path;
                    if (file_exists($admin_path)) {
                        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Файл доступен из админ-панели</p>";
                    } else {
                        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Файл НЕ доступен из админ-панели</p>";
                        $all_tests_passed = false;
                    }
                    
                    // Clean up
                    unlink($screenshot_path);
                } else {
                    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Файл НЕ существует физически</p>";
                    $all_tests_passed = false;
                }
            } else {
                echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>В БД нет пути к скриншоту</p>";
                $all_tests_passed = false;
            }
        } catch (Exception $e) {
            echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка БД: " . $e->getMessage() . "</p>";
            $all_tests_passed = false;
        }
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка processDepositRequest: " . $result['message'] . "</p>";
        $all_tests_passed = false;
    }
} else {
    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Функция processDepositRequest не найдена</p>";
    $all_tests_passed = false;
}

// Clean up test file
if (file_exists($test_file_path)) {
    unlink($test_file_path);
}

echo "</div>";

// Test 3: Check unified system compatibility
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🔄 Тест 3: Совместимость с унифицированной системой</h2>";

$system_checks = [
    'Унифицированная директория существует' => is_dir('uploads/transactions/'),
    'Унифицированная директория доступна для записи' => is_writable('uploads/transactions/'),
    'Дублирующая админ-директория отсутствует' => !is_dir('admin/uploads/transactions/'),
    'Функция uploadTransactionScreenshot унифицирована' => function_exists('uploadTransactionScreenshot') && 
        !strpos(file_get_contents('includes/functions.php'), 'admin/uploads/transactions')
];

foreach ($system_checks as $check => $result) {
    $color = $result ? 'green' : 'red';
    $icon = $result ? 'check' : 'times';
    echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
    
    if (!$result) {
        $all_tests_passed = false;
    }
}

echo "</div>";

// Test 4: Compare with working test files
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>⚖️ Тест 4: Сравнение с рабочими тестами</h2>";

$test_file = 'test_unified_system.php';
if (file_exists($test_file)) {
    $test_content = file_get_contents($test_file);
    $dashboard_content = file_get_contents($dashboard_file);
    
    $comparison_checks = [
        'Оба используют processDepositRequest' => 
            strpos($test_content, 'processDepositRequest') !== false && 
            strpos($dashboard_content, 'processDepositRequest') !== false,
        'Оба обрабатывают $_FILES[transaction_screenshot]' => 
            strpos($test_content, '$_FILES[\'transaction_screenshot\']') !== false && 
            strpos($dashboard_content, '$_FILES[\'transaction_screenshot\']') !== false,
        'Оба имеют CSRF защиту' => 
            strpos($test_content, 'csrf_token') !== false && 
            strpos($dashboard_content, 'csrf_token') !== false,
        'Dashboard больше НЕ делает редирект' => 
            strpos($dashboard_content, "redirect('dashboard.php');") === false,
        'Оба отображают результат на той же странице' => 
            strpos($test_content, 'bg-green-50') !== false && 
            strpos($dashboard_content, '$success_message') !== false
    ];
    
    foreach ($comparison_checks as $check => $result) {
        $color = $result ? 'green' : 'red';
        $icon = $result ? 'check' : 'times';
        echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
        
        if (!$result) {
            $all_tests_passed = false;
        }
    }
} else {
    echo "<p class='text-yellow-600'><i class='fas fa-exclamation-triangle mr-2'></i>Тестовый файл не найден для сравнения</p>";
}

echo "</div>";

// Final Results
if ($all_tests_passed) {
    echo "<div class='bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl p-8 text-white text-center'>
            <div class='text-6xl mb-4'>
                <i class='fas fa-trophy'></i>
            </div>
            <h2 class='text-3xl font-bold mb-4'>🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!</h2>
            <p class='text-xl mb-6 opacity-90'>Dashboard.php теперь работает идентично тестовым файлам</p>
            
            <div class='bg-white bg-opacity-20 rounded-lg p-6 mb-6'>
                <h3 class='text-xl font-bold mb-3'>✅ Достигнутые результаты:</h3>
                <ul class='text-left space-y-2 opacity-90'>
                    <li>• Убраны все редиректы после POST-запросов</li>
                    <li>• Сообщения отображаются на той же странице</li>
                    <li>• Загрузка файлов работает в модальном окне</li>
                    <li>• Файлы сохраняются в унифицированную директорию</li>
                    <li>• Совместимость с админ-панелью</li>
                    <li>• Полная функциональность как в тестовых файлах</li>
                </ul>
            </div>
            
            <div class='flex flex-wrap justify-center gap-4'>
                <a href='pages/dashboard.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-tachometer-alt mr-2'></i>Тест Dashboard
                </a>
                <a href='admin/transactions.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-cog mr-2'></i>Админ-панель
                </a>
                <a href='test_unified_system.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-check-double mr-2'></i>Тест системы
                </a>
                <a href='unified_system_report.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-chart-line mr-2'></i>Отчет системы
                </a>
            </div>
          </div>";
} else {
    echo "<div class='bg-red-50 border-2 border-red-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-red-500 mb-4'>
                <i class='fas fa-exclamation-triangle'></i>
            </div>
            <h2 class='text-3xl font-bold text-red-900 mb-4'>⚠️ Некоторые тесты не прошли</h2>
            <p class='text-red-700 text-lg mb-6'>Требуется дополнительная диагностика</p>
            
            <div class='flex flex-wrap justify-center gap-4'>
                <a href='compare_working_vs_broken.php' class='bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-search mr-2'></i>Диагностика
                </a>
                <a href='fix_dashboard_final.php' class='bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-wrench mr-2'></i>Повторить исправления
                </a>
                <a href='pages/dashboard_simplified.php' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-rocket mr-2'></i>Упрощенная версия
                </a>
            </div>
          </div>";
}

echo "</div></body></html>";
?>
