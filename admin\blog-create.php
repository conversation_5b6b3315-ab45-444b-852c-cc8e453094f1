<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../pages/login.php');
}

// Handle blog post creation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', 'Invalid security token');
    } else {
        $title = sanitizeInput($_POST['title'] ?? '');
        $content = $_POST['content'] ?? '';
        $excerpt = sanitizeInput($_POST['excerpt'] ?? '');
        $category = sanitizeInput($_POST['category'] ?? 'general');
        $tags = sanitizeInput($_POST['tags'] ?? '');
        $meta_title = sanitizeInput($_POST['meta_title'] ?? '');
        $meta_description = sanitizeInput($_POST['meta_description'] ?? '');
        $is_published = isset($_POST['is_published']) ? 1 : 0;
        
        // Validation
        $errors = [];
        if (empty($title)) {
            $errors[] = 'Title is required';
        }
        if (empty($content)) {
            $errors[] = 'Content is required';
        }
        
        if (empty($errors)) {
            // Generate slug from title
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
            
            $db = getDB();
            
            // Check if slug already exists
            $stmt = $db->prepare("SELECT id FROM blog_posts WHERE slug = ?");
            $stmt->execute([$slug]);
            if ($stmt->fetch()) {
                $slug .= '-' . time();
            }
            
            $stmt = $db->prepare("
                INSERT INTO blog_posts (title, slug, content, excerpt, category, tags, meta_title, meta_description, is_published) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            if ($stmt->execute([$title, $slug, $content, $excerpt, $category, $tags, $meta_title, $meta_description, $is_published])) {
                setFlashMessage('success', 'Blog post created successfully');
                redirect('blog.php');
            } else {
                setFlashMessage('error', 'Failed to create blog post');
            }
        } else {
            setFlashMessage('error', implode(', ', $errors));
        }
    }
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Create New Blog Post</h1>
                <p class="text-gray-600 mt-2">Write and publish a new blog post</p>
            </div>
            <a href="blog.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300">
                <i class="fas fa-arrow-left mr-2"></i>Back to Blog
            </a>
        </div>

        <!-- Flash Messages -->
        <?php
        $success_message = getFlashMessage('success');
        $error_message = getFlashMessage('error');
        ?>

        <?php if ($success_message): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                <i class="fas fa-check-circle mr-2"></i><?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- Blog Post Form -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100 p-6">
            <form method="POST" class="space-y-6">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <!-- Title -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                    <input type="text" name="title" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Enter blog post title">
                </div>

                <!-- Category and Tags -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="general">General</option>
                            <option value="investment">Investment</option>
                            <option value="real-estate">Real Estate</option>
                            <option value="yachts">Yachts</option>
                            <option value="news">News</option>
                            <option value="guides">Guides</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                        <input type="text" name="tags"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="investment, luxury, real-estate">
                        <p class="text-xs text-gray-500 mt-1">Separate tags with commas</p>
                    </div>
                </div>

                <!-- Excerpt -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Excerpt</label>
                    <textarea name="excerpt" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Brief description of the blog post"></textarea>
                    <p class="text-xs text-gray-500 mt-1">Short summary for previews and social media</p>
                </div>

                <!-- Content -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Content *</label>
                    <textarea name="content" rows="15" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Write your blog post content here..."></textarea>
                    <p class="text-xs text-gray-500 mt-1">You can use HTML tags for formatting</p>
                </div>

                <!-- SEO Settings -->
                <div class="border-t border-gray-200 pt-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">SEO Settings</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                            <input type="text" name="meta_title"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="SEO title for search engines">
                            <p class="text-xs text-gray-500 mt-1">Leave empty to use the post title</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                            <textarea name="meta_description" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                      placeholder="SEO description for search engines"></textarea>
                            <p class="text-xs text-gray-500 mt-1">Recommended length: 150-160 characters</p>
                        </div>
                    </div>
                </div>

                <!-- Publication Status -->
                <div class="border-t border-gray-200 pt-6">
                    <div class="flex items-center">
                        <input type="checkbox" name="is_published" id="is_published" 
                               class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                        <label for="is_published" class="ml-2 text-sm font-medium text-gray-700">
                            Publish immediately
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Uncheck to save as draft</p>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="blog.php" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                        Cancel
                    </a>
                    <button type="submit" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-save mr-2"></i>Create Post
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
