<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../pages/login.php');
}

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', 'Invalid security token');
    } else {
        $settings = [
            'site_name' => sanitizeInput($_POST['site_name'] ?? ''),
            'site_description' => sanitizeInput($_POST['site_description'] ?? ''),
            'contact_email' => sanitizeInput($_POST['contact_email'] ?? ''),
            'contact_phone' => sanitizeInput($_POST['contact_phone'] ?? ''),
            'company_address' => sanitizeInput($_POST['company_address'] ?? ''),
            'usdt_wallet_address' => sanitizeInput($_POST['usdt_wallet_address'] ?? ''),
            'facebook_url' => sanitizeInput($_POST['facebook_url'] ?? ''),
            'twitter_url' => sanitizeInput($_POST['twitter_url'] ?? ''),
            'instagram_url' => sanitizeInput($_POST['instagram_url'] ?? ''),
            'linkedin_url' => sanitizeInput($_POST['linkedin_url'] ?? ''),
            'maintenance_mode' => isset($_POST['maintenance_mode']) ? '1' : '0'
        ];
        
        $updated = 0;
        foreach ($settings as $key => $value) {
            if (updateSiteSetting($key, $value)) {
                $updated++;
            }
        }
        
        if ($updated > 0) {
            setFlashMessage('success', "Updated $updated settings successfully");
        } else {
            setFlashMessage('error', 'Failed to update settings');
        }
        
        redirect('settings.php');
    }
}

// Get current settings
$current_settings = getAllSiteSettings();

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Site Settings</h1>
                <p class="text-gray-600 mt-2">Manage your website configuration</p>
            </div>
            <a href="index.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300">
                <i class="fas fa-arrow-left mr-2"></i>Back to Admin
            </a>
        </div>

        <!-- Flash Messages -->
        <?php
        $success_message = getFlashMessage('success');
        $error_message = getFlashMessage('error');
        ?>

        <?php if ($success_message): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                <i class="fas fa-check-circle mr-2"></i><?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <form method="POST" class="space-y-8">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

            <!-- General Settings -->
            <div class="bg-white rounded-xl card-shadow border border-gray-100 p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-cog text-blue-500 mr-3"></i>
                    General Settings
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                        <input type="text" name="site_name" value="<?php echo htmlspecialchars($current_settings['site_name'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                        <input type="email" name="contact_email" value="<?php echo htmlspecialchars($current_settings['contact_email'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
                        <textarea name="site_description" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"><?php echo htmlspecialchars($current_settings['site_description'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-xl card-shadow border border-gray-100 p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-address-book text-green-500 mr-3"></i>
                    Contact Information
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="text" name="contact_phone" value="<?php echo htmlspecialchars($current_settings['contact_phone'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">TRC-20 USDT Wallet</label>
                        <input type="text" name="usdt_wallet_address" value="<?php echo htmlspecialchars($current_settings['usdt_wallet_address'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Company Address</label>
                        <textarea name="company_address" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"><?php echo htmlspecialchars($current_settings['company_address'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Social Media -->
            <div class="bg-white rounded-xl card-shadow border border-gray-100 p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-share-alt text-purple-500 mr-3"></i>
                    Social Media Links
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fab fa-facebook text-blue-600 mr-2"></i>Facebook URL
                        </label>
                        <input type="url" name="facebook_url" value="<?php echo htmlspecialchars($current_settings['facebook_url'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fab fa-twitter text-blue-400 mr-2"></i>Twitter URL
                        </label>
                        <input type="url" name="twitter_url" value="<?php echo htmlspecialchars($current_settings['twitter_url'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fab fa-instagram text-pink-500 mr-2"></i>Instagram URL
                        </label>
                        <input type="url" name="instagram_url" value="<?php echo htmlspecialchars($current_settings['instagram_url'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fab fa-linkedin text-blue-700 mr-2"></i>LinkedIn URL
                        </label>
                        <input type="url" name="linkedin_url" value="<?php echo htmlspecialchars($current_settings['linkedin_url'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- System Settings -->
            <div class="bg-white rounded-xl card-shadow border border-gray-100 p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-server text-red-500 mr-3"></i>
                    System Settings
                </h2>
                
                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" name="maintenance_mode" id="maintenance_mode" 
                               <?php echo ($current_settings['maintenance_mode'] ?? '0') === '1' ? 'checked' : ''; ?>
                               class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                        <label for="maintenance_mode" class="ml-2 text-sm font-medium text-gray-700">
                            Enable Maintenance Mode
                        </label>
                    </div>
                    <p class="text-sm text-gray-500">When enabled, only administrators can access the site</p>
                </div>
            </div>

            <!-- Save Button -->
            <div class="flex justify-end">
                <button type="submit" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-save mr-2"></i>Save Settings
                </button>
            </div>
        </form>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
