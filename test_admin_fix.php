<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'testuser';
    $_SESSION['email'] = '<EMAIL>';
}

$user_id = $_SESSION['user_id'];

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Тест исправления админ-панели</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>🧪 Тест исправления админ-панели</h1>";

// Handle test upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'test_admin_upload') {
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-2xl font-bold text-gray-900 mb-4'>📤 Результат тестовой загрузки</h2>";
    
    if (verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $amount = floatval($_POST['amount'] ?? 0);
        $screenshot_file = $_FILES['admin_test_screenshot'] ?? null;
        
        $result = processDepositRequest($user_id, $amount, $screenshot_file);
        
        if ($result['success']) {
            echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4 mb-4'>
                    <h4 class='font-bold text-green-900'><i class='fas fa-check mr-2'></i>Загрузка успешна!</h4>
                    <p class='text-green-700'>" . htmlspecialchars($result['message']) . "</p>
                  </div>";
            
            // Get the latest transaction
            try {
                $db = getDB();
                $stmt = $db->prepare("SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
                $stmt->execute([$user_id]);
                $latest_transaction = $stmt->fetch();
                
                if ($latest_transaction && !empty($latest_transaction['screenshot_path'])) {
                    $screenshot_path = $latest_transaction['screenshot_path'];
                    
                    echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4'>
                            <h4 class='font-bold text-blue-900'><i class='fas fa-info mr-2'></i>Проверка доступности файла:</h4>";
                    
                    // Test different access paths
                    $test_paths = [
                        $screenshot_path => 'Основной путь',
                        '../' . $screenshot_path => 'Относительный путь из admin/',
                        'admin/uploads/transactions/' . basename($screenshot_path) => 'Админ-директория',
                        'admin/../' . $screenshot_path => 'Комбинированный путь'
                    ];
                    
                    foreach ($test_paths as $path => $description) {
                        $exists = file_exists($path);
                        $color = $exists ? 'green' : 'red';
                        $icon = $exists ? 'check' : 'times';
                        
                        echo "<p class='text-{$color}-700 text-sm'><i class='fas fa-{$icon} mr-1'></i>$description: " . ($exists ? 'Доступен' : 'НЕ доступен') . "</p>";
                    }
                    
                    echo "</div>";
                    
                    // Display image if accessible
                    if (file_exists($screenshot_path)) {
                        echo "<div class='bg-gray-50 border border-gray-200 rounded-lg p-4'>
                                <h4 class='font-bold text-gray-900 mb-2'>Предварительный просмотр:</h4>
                                <img src='$screenshot_path' alt='Uploaded screenshot' class='max-w-md rounded-lg shadow-lg border'>
                              </div>";
                    }
                }
            } catch (Exception $e) {
                echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>
                        <h4 class='font-bold text-red-900'><i class='fas fa-times mr-2'></i>Ошибка БД</h4>
                        <p class='text-red-700'>" . $e->getMessage() . "</p>
                      </div>";
            }
        } else {
            echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>
                    <h4 class='font-bold text-red-900'><i class='fas fa-times mr-2'></i>Ошибка загрузки</h4>
                    <p class='text-red-700'>" . htmlspecialchars($result['message']) . "</p>
                  </div>";
        }
    } else {
        echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>
                <h4 class='font-bold text-red-900'><i class='fas fa-times mr-2'></i>Ошибка CSRF</h4>
                <p class='text-red-700'>Недействительный токен безопасности</p>
              </div>";
    }
    
    echo "</div>";
}

// Test upload form
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📤 Тестовая форма для проверки админ-доступа</h2>
        <form method='POST' enctype='multipart/form-data' class='space-y-4'>
            <input type='hidden' name='csrf_token' value='" . generateCSRFToken() . "'>
            <input type='hidden' name='action' value='test_admin_upload'>
            
            <div>
                <label class='block text-sm font-medium text-gray-700 mb-2'>Сумма депозита (USDT)</label>
                <input type='number' name='amount' step='0.01' min='10' required value='30'
                       class='w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'>
            </div>
            
            <div>
                <label class='block text-sm font-medium text-gray-700 mb-2'>Скриншот транзакции</label>
                <input type='file' name='admin_test_screenshot' accept='image/*' required
                       class='block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'>
                <p class='text-sm text-gray-500 mt-1'>Загрузите изображение для тестирования админ-доступа</p>
            </div>
            
            <button type='submit' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-upload mr-2'></i>Загрузить и протестировать
            </button>
        </form>
      </div>";

// Check current system status
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🔍 Статус системы</h2>";

$system_checks = [
    'Основная директория uploads/transactions/' => is_dir('uploads/transactions/'),
    'Админ-директория admin/uploads/transactions/' => is_dir('admin/uploads/transactions/'),
    'Функция uploadTransactionScreenshot обновлена' => function_exists('uploadTransactionScreenshot'),
    'Основная директория доступна для записи' => is_writable('uploads/transactions/'),
    'Админ-директория доступна для записи' => is_writable('admin/uploads/transactions/')
];

foreach ($system_checks as $check => $result) {
    $color = $result ? 'green' : 'red';
    $icon = $result ? 'check' : 'times';
    echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
}

echo "</div>";

// Display existing files in both directories
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📁 Файлы в директориях</h2>";

$directories = [
    'uploads/transactions/' => 'Основная директория',
    'admin/uploads/transactions/' => 'Админ-директория'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-6'>";

foreach ($directories as $dir => $title) {
    echo "<div class='border rounded-lg p-4'>
            <h3 class='font-bold text-gray-900 mb-3'>$title</h3>
            <p class='text-sm text-gray-600 mb-3'>Путь: <code class='bg-gray-200 px-1 rounded'>$dir</code></p>";
    
    if (is_dir($dir)) {
        $files = glob($dir . '*');
        
        if (empty($files)) {
            echo "<p class='text-gray-500 text-sm'>Нет файлов</p>";
        } else {
            echo "<div class='space-y-2'>";
            foreach (array_slice($files, 0, 5) as $file) {
                $filename = basename($file);
                $filesize = number_format(filesize($file) / 1024, 2);
                
                echo "<div class='flex items-center justify-between text-sm'>
                        <span class='text-gray-700'>$filename</span>
                        <span class='text-gray-500'>{$filesize} KB</span>
                      </div>";
            }
            
            if (count($files) > 5) {
                echo "<p class='text-gray-500 text-sm'>... и еще " . (count($files) - 5) . " файлов</p>";
            }
            
            echo "</div>";
        }
    } else {
        echo "<p class='text-red-500 text-sm'>Директория не существует</p>";
    }
    
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Action buttons
echo "<div class='text-center space-x-4'>
        <a href='admin/transactions.php' class='bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg transition-colors'>
            <i class='fas fa-cog mr-2'></i>Проверить админ-панель
        </a>
        <a href='pages/dashboard.php' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
            <i class='fas fa-tachometer-alt mr-2'></i>Основной Dashboard
        </a>
        <a href='fix_admin_access.php' class='bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors'>
            <i class='fas fa-wrench mr-2'></i>Повторить исправления
        </a>
        <a href='path_fix_final_report.php' class='bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg transition-colors'>
            <i class='fas fa-chart-line mr-2'></i>Финальный отчет
        </a>
      </div>";

echo "</div></body></html>";
?>
