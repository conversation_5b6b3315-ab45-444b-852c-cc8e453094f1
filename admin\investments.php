<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireAdmin();

$page_title = 'Управление инвестициями';

// Handle investment operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', 'Недействительный токен безопасности');
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'add') {
            $title = sanitizeInput($_POST['title'] ?? '');
            $description = sanitizeInput($_POST['description'] ?? '');
            $category = sanitizeInput($_POST['category'] ?? '');
            $min_amount = floatval($_POST['min_amount'] ?? 0);
            $max_amount = floatval($_POST['max_amount'] ?? 0);
            $monthly_rate = floatval($_POST['monthly_rate'] ?? 0);
            $duration_months = intval($_POST['duration_months'] ?? 0);
            $image_url = sanitizeInput($_POST['image_url'] ?? '');
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            
            if ($title && $description && $min_amount > 0 && $monthly_rate > 0) {
                $db = getDB();
                $stmt = $db->prepare("
                    INSERT INTO investments (title, description, category, min_amount, max_amount, monthly_rate, duration_months, image_url, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                if ($stmt->execute([$title, $description, $category, $min_amount, $max_amount, $monthly_rate, $duration_months, $image_url, $is_active])) {
                    setFlashMessage('success', 'Инвестиция успешно добавлена');
                } else {
                    setFlashMessage('error', 'Ошибка при добавлении инвестиции');
                }
            } else {
                setFlashMessage('error', 'Пожалуйста, заполните все обязательные поля');
            }
        }
        
        elseif ($action === 'edit') {
            $id = intval($_POST['id'] ?? 0);
            $title = sanitizeInput($_POST['title'] ?? '');
            $description = sanitizeInput($_POST['description'] ?? '');
            $category = sanitizeInput($_POST['category'] ?? '');
            $min_amount = floatval($_POST['min_amount'] ?? 0);
            $max_amount = floatval($_POST['max_amount'] ?? 0);
            $monthly_rate = floatval($_POST['monthly_rate'] ?? 0);
            $duration_months = intval($_POST['duration_months'] ?? 0);
            $image_url = sanitizeInput($_POST['image_url'] ?? '');
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            
            if ($id && $title && $description && $min_amount > 0 && $monthly_rate > 0) {
                $db = getDB();
                $stmt = $db->prepare("
                    UPDATE investments 
                    SET title = ?, description = ?, category = ?, min_amount = ?, max_amount = ?, 
                        monthly_rate = ?, duration_months = ?, image_url = ?, is_active = ?
                    WHERE id = ?
                ");
                
                if ($stmt->execute([$title, $description, $category, $min_amount, $max_amount, $monthly_rate, $duration_months, $image_url, $is_active, $id])) {
                    setFlashMessage('success', 'Инвестиция успешно обновлена');
                } else {
                    setFlashMessage('error', 'Ошибка при обновлении инвестиции');
                }
            } else {
                setFlashMessage('error', 'Пожалуйста, заполните все обязательные поля');
            }
        }
        
        elseif ($action === 'toggle_status') {
            $id = intval($_POST['id'] ?? 0);
            if ($id) {
                $db = getDB();
                $stmt = $db->prepare("UPDATE investments SET is_active = NOT is_active WHERE id = ?");
                if ($stmt->execute([$id])) {
                    setFlashMessage('success', 'Статус инвестиции изменен');
                } else {
                    setFlashMessage('error', 'Ошибка при изменении статуса');
                }
            }
        }
        
        elseif ($action === 'delete') {
            $id = intval($_POST['id'] ?? 0);
            if ($id) {
                $db = getDB();
                
                // Check if investment has active user investments
                $stmt = $db->prepare("SELECT COUNT(*) as count FROM user_investments WHERE investment_id = ? AND is_active = 1");
                $stmt->execute([$id]);
                $active_count = $stmt->fetch()['count'];
                
                if ($active_count > 0) {
                    setFlashMessage('error', 'Нельзя удалить инвестицию с активными вложениями пользователей');
                } else {
                    $stmt = $db->prepare("DELETE FROM investments WHERE id = ?");
                    if ($stmt->execute([$id])) {
                        setFlashMessage('success', 'Инвестиция успешно удалена');
                    } else {
                        setFlashMessage('error', 'Ошибка при удалении инвестиции');
                    }
                }
            }
        }
    }
    
    redirect('investments.php');
}

// Get all investments
$db = getDB();
$stmt = $db->prepare("
    SELECT i.*, 
           COUNT(ui.id) as total_investors,
           SUM(ui.amount) as total_invested
    FROM investments i
    LEFT JOIN user_investments ui ON i.id = ui.investment_id AND ui.is_active = 1
    GROUP BY i.id
    ORDER BY i.created_at DESC
");
$stmt->execute();
$investments = $stmt->fetchAll();

// Get investment for editing if requested
$edit_investment = null;
if (isset($_GET['edit'])) {
    $edit_id = intval($_GET['edit']);
    $stmt = $db->prepare("SELECT * FROM investments WHERE id = ?");
    $stmt->execute([$edit_id]);
    $edit_investment = $stmt->fetch();
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Flash Messages -->
        <?php
        $success_message = getFlashMessage('success');
        $error_message = getFlashMessage('error');
        ?>

        <?php if ($success_message): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg shadow-lg animate-fade-in" id="success-alert">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-3"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                    <button onclick="closeAlert('success-alert')" class="text-green-500 hover:text-green-700 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg shadow-lg animate-fade-in" id="error-alert">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-500 mr-3"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                    <button onclick="closeAlert('error-alert')" class="text-red-500 hover:text-red-700 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Управление инвестициями</h1>
                <p class="text-gray-600 mt-2">Создание и управление инвестиционными возможностями</p>
            </div>
            <div class="flex space-x-3">
                <button onclick="openAddModal()" class="bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-plus mr-2"></i>Добавить инвестицию
                </button>
                <a href="index.php" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-arrow-left mr-2"></i>Назад к панели
                </a>
            </div>
        </div>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Всего инвестиций</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo count($investments); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Активные</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo count(array_filter($investments, function($i) { return $i['is_active']; })); ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Всего инвесторов</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo array_sum(array_column($investments, 'total_investors')); ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Общая сумма</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo formatCurrency(array_sum(array_column($investments, 'total_invested'))); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Investments List -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Список инвестиций</h2>
            </div>
            <div class="p-6">
                <?php if (empty($investments)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-chart-line text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-600">Пока нет созданных инвестиций</p>
                        <button onclick="openAddModal()" class="mt-4 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                            Создать первую инвестицию
                        </button>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                        <?php foreach ($investments as $investment): ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-300 <?php echo $investment['is_active'] ? 'bg-white' : 'bg-gray-50'; ?>">
                                <div class="flex justify-between items-start mb-3">
                                    <div class="flex-1">
                                        <h3 class="font-semibold text-gray-900 mb-1"><?php echo htmlspecialchars($investment['title']); ?></h3>
                                        <p class="text-sm text-gray-600 mb-2"><?php echo htmlspecialchars(substr($investment['description'], 0, 100)) . '...'; ?></p>
                                        <span class="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                            <?php echo ucfirst(str_replace('_', ' ', $investment['category'])); ?>
                                        </span>
                                    </div>
                                    <div class="ml-3">
                                        <?php if ($investment['image_url']): ?>
                                            <img src="<?php echo htmlspecialchars($investment['image_url']); ?>"
                                                 alt="<?php echo htmlspecialchars($investment['title']); ?>"
                                                 class="w-16 h-16 object-cover rounded-lg">
                                        <?php else: ?>
                                            <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-image text-gray-400"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4 mb-4 text-sm">
                                    <div>
                                        <p class="text-gray-600">Мин. сумма:</p>
                                        <p class="font-semibold"><?php echo formatCurrency($investment['min_amount']); ?></p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600">Макс. сумма:</p>
                                        <p class="font-semibold"><?php echo $investment['max_amount'] ? formatCurrency($investment['max_amount']) : 'Без лимита'; ?></p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600">Доходность:</p>
                                        <p class="font-semibold text-green-600"><?php echo formatPercentage($investment['monthly_rate']); ?>%/мес</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600">Срок:</p>
                                        <p class="font-semibold"><?php echo $investment['duration_months'] ? $investment['duration_months'] . ' мес.' : 'Бессрочно'; ?></p>
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4 mb-4 text-sm">
                                    <div>
                                        <p class="text-gray-600">Инвесторы:</p>
                                        <p class="font-semibold"><?php echo $investment['total_investors']; ?></p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600">Вложено:</p>
                                        <p class="font-semibold"><?php echo formatCurrency($investment['total_invested'] ?: 0); ?></p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between">
                                    <span class="px-2 py-1 rounded-full text-xs font-semibold <?php
                                        echo $investment['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                                    ?>">
                                        <?php echo $investment['is_active'] ? 'Активна' : 'Неактивна'; ?>
                                    </span>

                                    <div class="flex space-x-1">
                                        <button onclick="editInvestment(<?php echo htmlspecialchars(json_encode($investment)); ?>)"
                                                class="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600 transition-colors">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form method="POST" class="inline" onsubmit="return confirm('Изменить статус инвестиции?')">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="id" value="<?php echo $investment['id']; ?>">
                                            <button type="submit" class="<?php echo $investment['is_active'] ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-green-500 hover:bg-green-600'; ?> text-white px-2 py-1 rounded text-xs transition-colors">
                                                <i class="fas <?php echo $investment['is_active'] ? 'fa-pause' : 'fa-play'; ?>"></i>
                                            </button>
                                        </form>
                                        <form method="POST" class="inline" onsubmit="return confirm('Вы уверены, что хотите удалить эту инвестицию?')">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?php echo $investment['id']; ?>">
                                            <button type="submit" class="bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600 transition-colors">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Investment Modal -->
<div id="investmentModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-2xl w-full p-6 border border-gray-100 shadow-2xl max-h-screen overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-900" id="modalTitle">Добавить инвестицию</h3>
                <button onclick="closeInvestmentModal()" class="text-gray-500 hover:text-gray-700 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <form method="POST" id="investmentForm">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="add" id="formAction">
                <input type="hidden" name="id" id="investmentId">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Название инвестиции *</label>
                        <input type="text" name="title" id="investmentTitle" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Описание *</label>
                        <textarea name="description" id="investmentDescription" rows="3" required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Категория</label>
                        <select name="category" id="investmentCategory"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="real_estate">Недвижимость</option>
                            <option value="stocks">Акции</option>
                            <option value="crypto">Криптовалюта</option>
                            <option value="bonds">Облигации</option>
                            <option value="commodities">Сырьевые товары</option>
                            <option value="forex">Форекс</option>
                            <option value="other">Другое</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">URL изображения</label>
                        <input type="url" name="image_url" id="investmentImageUrl"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="https://example.com/image.jpg">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Минимальная сумма ($) *</label>
                        <input type="number" name="min_amount" id="investmentMinAmount" step="0.01" min="1" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Максимальная сумма ($)</label>
                        <input type="number" name="max_amount" id="investmentMaxAmount" step="0.01" min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="0 = без лимита">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Месячная доходность (%) *</label>
                        <input type="number" name="monthly_rate" id="investmentMonthlyRate" step="0.01" min="0.01" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Срок (месяцы)</label>
                        <input type="number" name="duration_months" id="investmentDurationMonths" min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="0 = бессрочно">
                    </div>
                </div>

                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" id="investmentIsActive" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Активная инвестиция</span>
                    </label>
                </div>

                <div class="flex space-x-3">
                    <button type="button" onclick="closeInvestmentModal()"
                            class="flex-1 border border-gray-300 text-gray-700 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                        Отмена
                    </button>
                    <button type="submit" class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3 rounded-lg font-medium transition-all duration-300 shadow-lg">
                        Сохранить
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openAddModal() {
    document.getElementById('modalTitle').textContent = 'Добавить инвестицию';
    document.getElementById('formAction').value = 'add';
    document.getElementById('investmentForm').reset();
    document.getElementById('investmentModal').classList.remove('hidden');
}

function editInvestment(investment) {
    document.getElementById('modalTitle').textContent = 'Редактировать инвестицию';
    document.getElementById('formAction').value = 'edit';
    document.getElementById('investmentId').value = investment.id;
    document.getElementById('investmentTitle').value = investment.title;
    document.getElementById('investmentDescription').value = investment.description;
    document.getElementById('investmentCategory').value = investment.category;
    document.getElementById('investmentImageUrl').value = investment.image_url || '';
    document.getElementById('investmentMinAmount').value = investment.min_amount;
    document.getElementById('investmentMaxAmount').value = investment.max_amount || '';
    document.getElementById('investmentMonthlyRate').value = investment.monthly_rate;
    document.getElementById('investmentDurationMonths').value = investment.duration_months || '';
    document.getElementById('investmentIsActive').checked = investment.is_active == 1;
    document.getElementById('investmentModal').classList.remove('hidden');
}

function closeInvestmentModal() {
    document.getElementById('investmentModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('investmentModal').addEventListener('click', function(e) {
    if (e.target === this) closeInvestmentModal();
});

// Alert functions
function closeAlert(alertId) {
    const alert = document.getElementById(alertId);
    if (alert) {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            alert.remove();
        }, 300);
    }
}

// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('[id$="-alert"]');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                closeAlert(alert.id);
            }
        }, 5000);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
