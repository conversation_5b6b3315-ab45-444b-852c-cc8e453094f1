<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Update - Poseidon</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">Database Update</h1>
            
            <?php
            if (isset($_POST['update_db'])) {
                require_once '../config/database.php';
                
                try {
                    $db = getDB();
                    
                    echo '<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">';
                    echo '<h3 class="font-semibold text-blue-900 mb-2">Starting database update...</h3>';
                    
                    // Check if investments table exists and has data
                    try {
                        $stmt = $db->query("SELECT COUNT(*) as count FROM investments");
                        $result = $stmt->fetch();
                        echo '<p class="text-sm text-blue-800">Current investments count: ' . $result['count'] . '</p>';
                    } catch (Exception $e) {
                        echo '<p class="text-sm text-blue-800">Investments table does not exist or is empty</p>';
                    }
                    
                    // Drop and recreate investments table to ensure correct structure
                    $db->exec("DROP TABLE IF EXISTS investments");
                    echo '<p class="text-sm text-blue-800">✓ Dropped existing investments table</p>';

                    $createTableSQL = "
                    CREATE TABLE investments (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        title VARCHAR(255) NOT NULL,
                        description TEXT,
                        category ENUM('real_estate', 'stocks', 'crypto', 'bonds', 'commodities', 'forex', 'other') NOT NULL DEFAULT 'real_estate',
                        min_amount DECIMAL(15,2) NOT NULL DEFAULT 10.00,
                        max_amount DECIMAL(15,2) NULL,
                        monthly_rate DECIMAL(5,2) NOT NULL,
                        duration_months INT NULL,
                        image_url VARCHAR(500),
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )";

                    $db->exec($createTableSQL);
                    echo '<p class="text-sm text-blue-800">✓ Investments table created with correct structure</p>';
                    
                    $insertSQL = "
                    INSERT INTO investments (title, description, category, min_amount, max_amount, monthly_rate, duration_months, image_url, is_active) VALUES
                    ('Luxury Miami Penthouse', 'Premium oceanfront penthouse with stunning views and high-end amenities. Perfect for generating consistent rental income in the heart of Miami.', 'real_estate', 1000.00, 50000.00, 8.50, 24, 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=400', TRUE),
                    ('Monaco Yacht Charter', 'Exclusive 120ft luxury yacht available for premium charter services. High-demand location with excellent returns.', 'other', 2500.00, 100000.00, 12.00, 18, 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400', TRUE),
                    ('Dubai Marina Tower', 'High-rise luxury apartment in the heart of Dubai Marina with stunning views and modern amenities.', 'real_estate', 500.00, 25000.00, 7.25, 36, 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=400', TRUE),
                    ('Caribbean Sailing Yacht', 'Beautiful 80ft sailing yacht perfect for Caribbean charters. Year-round demand and experienced crew included.', 'other', 1500.00, 75000.00, 10.50, 24, 'https://images.unsplash.com/photo-1567899378494-47b22a2ae96a?w=400', TRUE),
                    ('Tech Stock Portfolio', 'Diversified portfolio of leading technology stocks with strong growth potential and dividend yields.', 'stocks', 100.00, 10000.00, 6.75, 12, 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=400', TRUE),
                    ('Cryptocurrency Fund', 'Managed cryptocurrency investment fund focusing on established coins with strong fundamentals.', 'crypto', 250.00, 15000.00, 15.00, 6, 'https://images.unsplash.com/photo-1621761191319-c6fb62004040?w=400', FALSE)
                    ";
                    
                    $db->exec($insertSQL);
                    echo '<p class="text-sm text-blue-800">✓ Sample data inserted</p>';
                    echo '</div>';
                    
                    // Verify the update
                    $stmt = $db->query("SELECT COUNT(*) as count FROM investments");
                    $result = $stmt->fetch();
                    
                    echo '<div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">';
                    echo '<h3 class="font-semibold text-green-900 mb-2">✅ Database update completed successfully!</h3>';
                    echo '<p class="text-sm text-green-800">Total investments in database: ' . $result['count'] . '</p>';
                    echo '</div>';
                    
                    // Show sample data
                    $stmt = $db->query("SELECT title, category, min_amount, monthly_rate, is_active FROM investments LIMIT 6");
                    $investments = $stmt->fetchAll();
                    
                    echo '<div class="bg-gray-50 border border-gray-200 rounded-lg p-4">';
                    echo '<h3 class="font-semibold text-gray-900 mb-3">Sample investments:</h3>';
                    echo '<div class="space-y-2">';
                    foreach ($investments as $inv) {
                        $status = $inv['is_active'] ? 'Active' : 'Inactive';
                        $statusClass = $inv['is_active'] ? 'text-green-600' : 'text-red-600';
                        echo '<div class="flex justify-between items-center text-sm">';
                        echo '<span><strong>' . htmlspecialchars($inv['title']) . '</strong> (' . ucfirst($inv['category']) . ')</span>';
                        echo '<span>Min: $' . number_format($inv['min_amount'], 2) . ' | Rate: ' . $inv['monthly_rate'] . '% | <span class="' . $statusClass . '">' . $status . '</span></span>';
                        echo '</div>';
                    }
                    echo '</div>';
                    echo '</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="bg-red-50 border border-red-200 rounded-lg p-4">';
                    echo '<h3 class="font-semibold text-red-900 mb-2">❌ Error updating database</h3>';
                    echo '<p class="text-sm text-red-800">' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '</div>';
                }
            } else {
                ?>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <h3 class="font-semibold text-yellow-900 mb-2">⚠️ Database Update Required</h3>
                    <p class="text-sm text-yellow-800 mb-3">
                        The investments table structure needs to be updated to match the admin panel expectations.
                        This will create/update the table with the correct fields and add sample data.
                    </p>
                    <p class="text-sm text-yellow-800">
                        <strong>What this will do:</strong><br>
                        • Create/update investments table with correct structure<br>
                        • Add sample investment data<br>
                        • Fix admin panel display issues
                    </p>
                </div>
                
                <form method="POST" class="text-center">
                    <button type="submit" name="update_db" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        Update Database
                    </button>
                </form>
                <?php
            }
            ?>
            
            <div class="mt-6 text-center">
                <a href="../admin/investments.php" class="text-blue-500 hover:text-blue-600 font-medium">
                    Go to Admin Panel →
                </a>
            </div>
        </div>
    </div>
</body>
</html>
