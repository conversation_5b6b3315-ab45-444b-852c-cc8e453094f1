-- Poseidon Rental Pro Database Schema
CREATE DATABASE IF NOT EXISTS poseidon_rental;
USE poseidon_rental;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    is_admin BOOLEAN DEFAULT FALSE,
    role ENUM('user', 'admin') DEFAULT 'user',
    status ENUM('active', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    reset_token VARCHAR(255) NULL,
    reset_expires DATETIME NULL
);

-- Investment assets table
CREATE TABLE investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category ENUM('real_estate', 'stocks', 'crypto', 'bonds', 'commodities', 'forex', 'other') NOT NULL DEFAULT 'real_estate',
    min_amount DECIMAL(15,2) NOT NULL DEFAULT 10.00,
    max_amount DECIMAL(15,2) NULL,
    monthly_rate DECIMAL(5,2) NOT NULL,
    duration_months INT NULL,
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User investments table
CREATE TABLE user_investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    investment_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    monthly_rate DECIMAL(5,2) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE CASCADE
);

-- Transactions table (deposits and withdrawals)
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdrawal') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    transaction_hash VARCHAR(255) NULL,
    wallet_address VARCHAR(255) NULL,
    screenshot_path VARCHAR(500) NULL,
    admin_notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Daily profits table
CREATE TABLE daily_profits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_investment_id INT NOT NULL,
    profit_amount DECIMAL(15,2) NOT NULL,
    profit_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_investment_id) REFERENCES user_investments(id) ON DELETE CASCADE,
    UNIQUE KEY unique_daily_profit (user_investment_id, profit_date)
);

-- Blog posts table
CREATE TABLE blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    category VARCHAR(50) DEFAULT 'general',
    tags TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    image_url VARCHAR(500),
    is_published BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact messages table
CREATE TABLE contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample admin user (password: admin123)
INSERT INTO users (username, email, password_hash, is_admin, role, status, balance) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', TRUE, 'admin', 'active', 0.00);

-- Insert sample investment assets
INSERT INTO investments (title, description, category, min_amount, max_amount, monthly_rate, duration_months, image_url) VALUES
('Luxury Miami Penthouse', 'Premium oceanfront penthouse with stunning views and high-end amenities. Perfect for generating consistent rental income in the heart of Miami.', 'real_estate', 1000.00, 50000.00, 8.50, 24, 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=400'),
('Monaco Yacht Charter', 'Exclusive 120ft luxury yacht available for premium charter services. High-demand location with excellent returns.', 'other', 2500.00, 100000.00, 12.00, 18, 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400'),
('Dubai Marina Tower', 'High-rise luxury apartment in the heart of Dubai Marina with stunning views and modern amenities.', 'real_estate', 500.00, 25000.00, 7.25, 36, 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=400'),
('Caribbean Sailing Yacht', 'Beautiful 80ft sailing yacht perfect for Caribbean charters. Year-round demand and experienced crew included.', 'other', 1500.00, 75000.00, 10.50, 24, 'https://images.unsplash.com/photo-1567899378494-47b22a2ae96a?w=400'),
('Tech Stock Portfolio', 'Diversified portfolio of leading technology stocks with strong growth potential and dividend yields.', 'stocks', 100.00, 10000.00, 6.75, 12, 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=400'),
('Cryptocurrency Fund', 'Managed cryptocurrency investment fund focusing on established coins with strong fundamentals.', 'crypto', 250.00, 15000.00, 15.00, 6, 'https://images.unsplash.com/photo-1621761191319-c6fb62004040?w=400');

-- Site settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Balance changes log table
CREATE TABLE IF NOT EXISTS balance_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    admin_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    operation ENUM('add', 'subtract') NOT NULL,
    notes TEXT,
    old_balance DECIMAL(10,2) NOT NULL,
    new_balance DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert sample blog posts
INSERT INTO blog_posts (title, slug, content, excerpt, image_url, is_published) VALUES
('Luxury Real Estate Market Trends 2024', 'luxury-real-estate-trends-2024', 'The luxury real estate market continues to show strong performance in 2024, with premium properties in key locations maintaining their value and generating consistent returns for investors. Our analysis shows that oceanfront properties and urban penthouses are particularly attractive for rental income generation.', 'Analysis of current luxury real estate market trends and investment opportunities.', '/assets/images/blog-realestate.jpg', TRUE),
('Yacht Charter Industry Growth', 'yacht-charter-industry-growth', 'The yacht charter industry has experienced unprecedented growth over the past year, with demand for luxury yacht experiences reaching new heights. This presents excellent opportunities for investors looking to diversify their portfolio with high-yield maritime assets.', 'Exploring the booming yacht charter market and investment potential.', '/assets/images/blog-yacht.jpg', TRUE);
