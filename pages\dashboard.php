<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireLogin();

$page_title = 'Dashboard';
$user_id = getCurrentUserId();

// Get dashboard statistics
$stats = getDashboardStats($user_id);

// Get user investments
$user_investments = getUserInvestments($user_id);

// Get recent transactions
$recent_transactions = getUserTransactions($user_id, 5);

// Handle deposit request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'deposit') {
    if (verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $amount = floatval($_POST['amount'] ?? 0);
        $screenshot_file = $_FILES['transaction_screenshot'] ?? null;

        // Debug information
        error_log("Deposit request received - Amount: $amount, File: " . print_r($screenshot_file, true));

        // Enhanced debug information for file upload
        if (isset($_FILES['transaction_screenshot'])) {
            error_log('File upload debug - Name: ' . ($_FILES['transaction_screenshot']['name'] ?? 'N/A'));
            error_log('File upload debug - Size: ' . ($_FILES['transaction_screenshot']['size'] ?? 'N/A'));
            error_log('File upload debug - Error: ' . ($_FILES['transaction_screenshot']['error'] ?? 'N/A'));
            error_log('File upload debug - Type: ' . ($_FILES['transaction_screenshot']['type'] ?? 'N/A'));
            error_log('File upload debug - Tmp name: ' . ($_FILES['transaction_screenshot']['tmp_name'] ?? 'N/A'));
        } else {
            error_log('File upload debug - No file in $_FILES[transaction_screenshot]');
        }

        $result = processDepositRequest($user_id, $amount, $screenshot_file);

        error_log("Deposit result: " . print_r($result, true));

        if ($result['success']) {
            $success_message = $result['message'];
            // Don't redirect - show success on same page
        } else {
            $error_message = $result['message'];
            // Don't redirect - show error on same page
        }
    } else {
        $error_message = 'Invalid security token. Please try again.';
        // Don't redirect - show error on same page
    }
}

// Handle withdrawal request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'withdrawal') {
    if (verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $amount = floatval($_POST['amount'] ?? 0);
        $wallet_address = sanitizeInput($_POST['wallet_address'] ?? '');
        
        $result = processWithdrawalRequest($user_id, $amount, $wallet_address);
        
        if ($result['success']) {
            $success_message = $result['message'];
            // Don't redirect - show success on same page
        } else {
            $error_message = $result['message'];
            // Don't redirect - show error on same page
        }
    }
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Success/Error Messages -->
        <?php
        // Messages are now set directly from form processing above
        // No need to get flash messages since we're not redirecting
        ?>

        <?php if ($success_message): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg shadow-lg animate-fade-in" id="success-alert">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-3"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                    <button onclick="closeAlert('success-alert')" class="text-green-500 hover:text-green-700 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg shadow-lg animate-fade-in" id="error-alert">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-500 mr-3"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                    <button onclick="closeAlert('error-alert')" class="text-red-500 hover:text-red-700 transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Welcome back, <?php echo htmlspecialchars($_SESSION['username']); ?>!</h1>
            <p class="text-gray-600 mt-2">Manage your investments and track your portfolio performance</p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-wallet text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Available Balance</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo formatCurrency($stats['balance']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Invested</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo formatCurrency($stats['total_invested']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-coins text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Profit</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo formatCurrency($stats['total_profit']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100 group hover:shadow-xl transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-briefcase text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Active Investments</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['active_investments']; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Active Investments -->
                <div class="bg-white rounded-xl card-shadow border border-gray-100">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-xl font-bold text-gray-900">Active Investments</h2>
                            <a href="investments.php" class="text-blue-600 hover:text-purple-600 font-semibold transition-colors">
                                Browse More →
                            </a>
                        </div>
                    </div>
                    <div class="p-6">
                        <?php if (empty($user_investments)): ?>
                            <div class="text-center py-8">
                                <i class="fas fa-chart-line text-gray-400 text-4xl mb-4"></i>
                                <p class="text-gray-600 mb-4">You haven't made any investments yet</p>
                                <a href="investments.php" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                                    Start Investing
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($user_investments as $investment): ?>
                                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-300 group">
                                        <div class="flex justify-between items-start">
                                            <div class="flex items-center space-x-4">
                                                <img src="<?php echo $investment['image_url'] ?: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'; ?>"
                                                     alt="<?php echo htmlspecialchars($investment['title']); ?>"
                                                     class="w-16 h-16 object-cover rounded-lg group-hover:scale-105 transition-transform duration-300">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors"><?php echo htmlspecialchars($investment['title']); ?></h3>
                                                    <p class="text-sm text-gray-600"><?php echo ucfirst(str_replace('_', ' ', $investment['category'])); ?></p>
                                                    <p class="text-sm text-gray-500">Started: <?php echo date('M j, Y', strtotime($investment['start_date'])); ?></p>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <p class="text-lg font-bold text-gray-900"><?php echo formatCurrency($investment['amount']); ?></p>
                                                <p class="text-sm text-green-600"><?php echo formatPercentage($investment['monthly_rate']); ?> monthly</p>
                                                <p class="text-sm text-gray-500">Profit: <?php echo formatCurrency($investment['total_profit']); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="bg-white rounded-xl card-shadow border border-gray-100">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-900">Recent Transactions</h2>
                    </div>
                    <div class="p-6">
                        <?php if (empty($recent_transactions)): ?>
                            <div class="text-center py-8">
                                <i class="fas fa-receipt text-gray-400 text-4xl mb-4"></i>
                                <p class="text-gray-600">No transactions yet</p>
                            </div>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <div class="flex justify-between items-center py-3 border-b border-gray-100 last:border-b-0">
                                        <div class="flex items-center space-x-3">
                                            <?php
                                            $iconClass = 'fa-arrow-down text-green-600';
                                            $bgClass = 'bg-green-100';
                                            $displayType = ucfirst($transaction['type']);

                                            if ($transaction['type'] === 'withdrawal') {
                                                $iconClass = 'fa-arrow-up text-red-600';
                                                $bgClass = 'bg-red-100';
                                            } elseif ($transaction['type'] === 'profit_credit') {
                                                $iconClass = 'fa-coins text-yellow-600';
                                                $bgClass = 'bg-yellow-100';
                                                $displayType = 'Daily Profit';
                                            }
                                            ?>
                                            <div class="w-10 h-10 rounded-full flex items-center justify-center <?php echo $bgClass; ?>">
                                                <i class="fas <?php echo $iconClass; ?>"></i>
                                            </div>
                                            <div>
                                                <p class="font-semibold"><?php echo $displayType; ?></p>
                                                <p class="text-sm text-gray-500"><?php echo timeAgo($transaction['created_at']); ?></p>
                                                <?php if ($transaction['type'] === 'profit_credit' && !empty($transaction['admin_notes'])): ?>
                                                    <p class="text-xs text-gray-400"><?php echo htmlspecialchars($transaction['admin_notes']); ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <?php
                                            $amountClass = 'text-green-600';
                                            $amountPrefix = '+';

                                            if ($transaction['type'] === 'withdrawal') {
                                                $amountClass = 'text-red-600';
                                                $amountPrefix = '-';
                                            } elseif ($transaction['type'] === 'profit_credit') {
                                                $amountClass = 'text-yellow-600';
                                                $amountPrefix = '+';
                                            }
                                            ?>
                                            <p class="font-semibold <?php echo $amountClass; ?>">
                                                <?php echo $amountPrefix; ?><?php echo formatCurrency($transaction['amount']); ?>
                                            </p>
                                            <span class="text-xs px-2 py-1 rounded-full <?php
                                                if ($transaction['status'] === 'completed') {
                                                    echo 'bg-green-100 text-green-800';
                                                } elseif ($transaction['status'] === 'approved') {
                                                    echo 'bg-green-100 text-green-800';
                                                } elseif ($transaction['status'] === 'rejected') {
                                                    echo 'bg-red-100 text-red-800';
                                                } else {
                                                    echo 'bg-yellow-100 text-yellow-800';
                                                }
                                            ?>">
                                                <?php echo ucfirst($transaction['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Actions -->
                <div class="bg-white rounded-xl card-shadow p-6 border border-gray-100">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <button onclick="openDepositModal()" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i class="fas fa-plus mr-2"></i>Deposit Funds
                        </button>
                        <button onclick="openWithdrawalModal()" class="w-full border-2 border-blue-500 text-blue-600 hover:bg-blue-50 py-3 rounded-lg font-semibold transition-all duration-300">
                            <i class="fas fa-minus mr-2"></i>Withdraw Funds
                        </button>
                        <a href="investments.php" class="block w-full text-center border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 py-3 rounded-lg font-semibold transition-all duration-300">
                            <i class="fas fa-search mr-2"></i>Browse Investments
                        </a>
                    </div>
                </div>

                <!-- Account Info -->
                <div class="bg-white rounded-xl card-shadow p-6 border border-gray-100">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Account Information</h3>
                    <div class="space-y-3">
                        <div>
                            <p class="text-sm text-gray-600">Username</p>
                            <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($_SESSION['username']); ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Email</p>
                            <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($_SESSION['email']); ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Member Since</p>
                            <p class="font-semibold text-gray-900"><?php echo date('M Y'); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Support -->
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-6 text-white shadow-lg">
                    <h3 class="text-lg font-bold mb-2">Need Help?</h3>
                    <p class="text-sm mb-4 text-blue-100">Our support team is available 24/7 to assist you</p>
                    <a href="contact.php" class="bg-white text-blue-600 px-4 py-2 rounded-lg font-semibold text-sm hover:bg-blue-50 transition-colors">
                        Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Deposit Modal -->
<div id="depositModal" class="fixed inset-0 bg-black bg-opacity-50 z-50" style="display: none;">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-2xl w-full p-6 border border-gray-100 shadow-2xl max-h-screen overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-900">Deposit Funds</h3>
                <button onclick="closeDepositModal()" class="text-gray-500 hover:text-gray-700 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Wallet Information Section -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h4 class="font-semibold text-blue-900 mb-3 flex items-center">
                    <i class="fas fa-wallet mr-2"></i>
                    TRC-20 USDT Deposit Address
                </h4>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <div class="bg-white p-3 rounded border border-blue-300 mb-3">
                            <p class="text-sm text-gray-600 mb-1">Wallet Address:</p>
                            <div class="flex items-center space-x-2">
                                <code id="walletAddress" class="text-sm font-mono bg-gray-100 px-2 py-1 rounded flex-1 break-all"><?php echo getSiteSetting('usdt_wallet_address', 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE'); ?></code>
                                <button onclick="copyWalletAddress()" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors" title="Copy Address">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>

                        <div class="text-sm text-gray-600 space-y-1">
                            <p><strong>Important:</strong></p>
                            <ul class="list-disc list-inside space-y-1 text-xs">
                                <li>Send only TRC-20 USDT</li>
                                <li>Minimum amount: $10</li>
                                <li>Processing: up to 24 hours</li>
                                <li>Save transaction hash</li>
                            </ul>
                        </div>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <h5 class="font-semibold text-yellow-900 mb-2 text-sm">Steps to Complete Deposit:</h5>
                        <ol class="text-xs text-yellow-800 space-y-1 list-decimal list-inside">
                            <li>Send USDT to the address above</li>
                            <li>Take a screenshot of the transaction</li>
                            <li>Upload the screenshot below</li>
                            <li>Submit your deposit request</li>
                        </ol>
                    </div>
                </div>
            </div>

            <form method="POST" enctype="multipart/form-data" onsubmit="return validateDepositForm(this)">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="deposit">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Deposit Amount (USDT)</label>
                    <input type="number" name="amount" step="0.01" min="10" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <p class="text-sm text-gray-500 mt-1">Minimum deposit amount: $10</p>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Transaction Screenshot</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                        <input type="file" name="transaction_screenshot" id="screenshotInput" accept="image/*" required
                               class="hidden" onchange="handleFileSelect(this)">
                        <label for="screenshotInput" class="cursor-pointer">
                            <div id="uploadArea">
                                <i class="fas fa-cloud-upload-alt text-gray-400 text-3xl mb-2"></i>
                                <p class="text-gray-600 mb-1">Click to upload transaction screenshot</p>
                                <p class="text-sm text-gray-500">JPG, PNG, GIF up to 5MB</p>
                            </div>
                        </label>
                    </div>
                    <div id="filePreview" class="mt-3 hidden">
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <i class="fas fa-file-image text-blue-500"></i>
                            <div class="flex-1">
                                <p id="fileName" class="text-sm font-medium text-gray-900"></p>
                                <p id="fileSize" class="text-xs text-gray-500"></p>
                            </div>
                            <button type="button" onclick="removeFile()" class="text-red-500 hover:text-red-700">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <p class="text-sm text-gray-500 mt-2">Upload a screenshot of your USDT transaction for verification</p>
                </div>

                <div class="flex space-x-3">
                    <button type="button" onclick="closeDepositModal()"
                            class="flex-1 border border-gray-300 text-gray-700 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                        Cancel
                    </button>
                    <button type="submit" class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3 rounded-lg font-medium transition-all duration-300 shadow-lg">
                        Submit Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Withdrawal Modal -->
<div id="withdrawalModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-lg w-full p-6 border border-gray-100 shadow-2xl">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-900">Withdraw Funds</h3>
                <button onclick="closeWithdrawalModal()" class="text-gray-500 hover:text-gray-700 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- TRC-20 Information -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <h4 class="font-semibold text-yellow-900 mb-2 flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Important Withdrawal Information
                </h4>
                <ul class="text-sm text-yellow-800 space-y-1">
                    <li>• Only TRC-20 USDT addresses supported</li>
                    <li>• Minimum withdrawal amount: $10</li>
                    <li>• Processing time: 24-48 hours</li>
                    <li>• Double-check address before submitting!</li>
                </ul>
            </div>

            <form method="POST" onsubmit="return validateWithdrawal()">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="withdrawal">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Withdrawal Amount (USDT)</label>
                    <input type="number" name="amount" step="0.01" min="10" max="<?php echo $stats['balance']; ?>" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <p class="text-sm text-gray-500 mt-1">Available: <?php echo formatCurrency($stats['balance']); ?></p>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">TRC-20 USDT Wallet Address</label>
                    <input type="text" name="wallet_address" id="walletAddressInput" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Enter your TRC-20 USDT address"
                           pattern="^T[A-Za-z0-9]{33}$"
                           title="Address must start with T and contain 34 characters">
                    <p class="text-sm text-gray-500 mt-1">Example: TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE</p>
                    <div id="addressValidation" class="mt-2 text-sm hidden"></div>
                </div>

                <div class="flex space-x-3">
                    <button type="button" onclick="closeWithdrawalModal()"
                            class="flex-1 border border-gray-300 text-gray-700 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                        Cancel
                    </button>
                    <button type="submit" class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3 rounded-lg font-medium transition-all duration-300 shadow-lg">
                        Submit Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Enhanced form validation for file upload
function validateDepositForm(form) {
    const fileInput = form.querySelector('input[name="transaction_screenshot"]');
    const amountInput = form.querySelector('input[name="amount"]');

    console.log('Form validation - File input:', fileInput);
    console.log('Form validation - Files:', fileInput ? fileInput.files : 'No file input');
    console.log('Form validation - Amount:', amountInput ? amountInput.value : 'No amount input');

    if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
        alert('Please select a transaction screenshot');
        return false;
    }

    const file = fileInput.files[0];
    console.log('Form validation - Selected file:', file);

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
        alert('Please select a valid image file (JPG, PNG, or GIF)');
        return false;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB');
        return false;
    }

    return true;
}

function openDepositModal() {
    document.getElementById('depositModal').style.display = 'flex';
}

function closeDepositModal() {
    document.getElementById('depositModal').style.display = 'none';
    // Reset file input
    document.getElementById('screenshotInput').value = '';
    document.getElementById('filePreview').classList.add('hidden');
}

function openWithdrawalModal() {
    document.getElementById('withdrawalModal').classList.remove('hidden');
}

function closeWithdrawalModal() {
    document.getElementById('withdrawalModal').classList.add('hidden');
}

// Copy wallet address to clipboard
function copyWalletAddress() {
    const walletAddress = document.getElementById('walletAddress').textContent;
    navigator.clipboard.writeText(walletAddress).then(function() {
        // Show success message
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('bg-green-500');
        button.classList.remove('bg-blue-500');

        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove('bg-green-500');
            button.classList.add('bg-blue-500');
        }, 2000);
    }).catch(function(err) {
        alert('Copy error: ' + err);
    });
}

// Handle file selection for transaction screenshot
function handleFileSelect(input) {
    const file = input.files[0];
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');

    if (file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a valid image file (JPG, PNG, or GIF)');
            input.value = '';
            return;
        }

        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB');
            input.value = '';
            return;
        }

        // Show file preview
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        filePreview.classList.remove('hidden');
    }
}

function removeFile() {
    document.getElementById('screenshotInput').value = '';
    document.getElementById('filePreview').classList.add('hidden');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Validate TRC-20 wallet address
function validateTRC20Address(address) {
    // TRC-20 addresses start with 'T' and are 34 characters long
    const trc20Pattern = /^T[A-Za-z0-9]{33}$/;
    return trc20Pattern.test(address);
}

// Real-time wallet address validation
document.addEventListener('DOMContentLoaded', function() {
    const walletInput = document.getElementById('walletAddressInput');
    const validationDiv = document.getElementById('addressValidation');

    if (walletInput && validationDiv) {
        walletInput.addEventListener('input', function() {
            const address = this.value.trim();

            if (address.length === 0) {
                validationDiv.classList.add('hidden');
                return;
            }

            validationDiv.classList.remove('hidden');

            if (validateTRC20Address(address)) {
                validationDiv.innerHTML = '<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>Valid TRC-20 address</span>';
                validationDiv.className = 'mt-2 text-sm text-green-600';
            } else {
                validationDiv.innerHTML = '<span class="text-red-600"><i class="fas fa-exclamation-circle mr-1"></i>Invalid TRC-20 address</span>';
                validationDiv.className = 'mt-2 text-sm text-red-600';
            }
        });
    }
});

// Validate withdrawal form
function validateWithdrawal() {
    const walletAddress = document.getElementById('walletAddressInput').value.trim();

    if (!validateTRC20Address(walletAddress)) {
        alert('Please enter a valid TRC-20 USDT address');
        return false;
    }

    return confirm('Are you sure you want to submit a withdrawal request? Please double-check your wallet address.');
}

// Close modals when clicking outside
document.getElementById('depositModal').addEventListener('click', function(e) {
    if (e.target === this) closeDepositModal();
});

document.getElementById('withdrawalModal').addEventListener('click', function(e) {
    if (e.target === this) closeWithdrawalModal();
});

// Alert functions
function closeAlert(alertId) {
    const alert = document.getElementById(alertId);
    if (alert) {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            alert.remove();
        }, 300);
    }
}

// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('[id$="-alert"]');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                closeAlert(alert.id);
            }
        }, 5000);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
