<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'testuser';
    $_SESSION['email'] = '<EMAIL>';
}

$user_id = $_SESSION['user_id'];

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Тест исправлений Dashboard</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>🧪 Тест исправлений Dashboard</h1>";

$all_tests_passed = true;

// Test 1: Check if dashboard.php has been fixed
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📄 Тест 1: Проверка исправлений в Dashboard.php</h2>";

$dashboard_file = 'pages/dashboard.php';
if (file_exists($dashboard_file)) {
    $dashboard_content = file_get_contents($dashboard_file);
    
    $fixes_check = [
        'Расширенная отладка добавлена' => strpos($dashboard_content, 'File upload debug - Name:') !== false,
        'Валидация формы добавлена' => strpos($dashboard_content, 'validateDepositForm') !== false,
        'onsubmit обработчик добавлен' => strpos($dashboard_content, 'onsubmit="return validateDepositForm(this)"') !== false,
        'Модальное окно использует style.display' => strpos($dashboard_content, 'style="display: none;"') !== false,
        'JavaScript обновлен для style.display' => strpos($dashboard_content, "style.display = 'flex'") !== false
    ];
    
    foreach ($fixes_check as $check => $result) {
        $color = $result ? 'green' : 'red';
        $icon = $result ? 'check' : 'times';
        echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
        
        if (!$result) {
            $all_tests_passed = false;
        }
    }
} else {
    echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Файл dashboard.php не найден</p>";
    $all_tests_passed = false;
}

echo "</div>";

// Test 2: Compare with working test_unified_system.php
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>⚖️ Тест 2: Сравнение с рабочим тестом</h2>";

$test_file = 'test_unified_system.php';
$dashboard_file = 'pages/dashboard.php';

if (file_exists($test_file) && file_exists($dashboard_file)) {
    $test_content = file_get_contents($test_file);
    $dashboard_content = file_get_contents($dashboard_file);
    
    // Extract key elements for comparison
    $comparison_checks = [
        'Оба используют enctype="multipart/form-data"' => 
            strpos($test_content, 'enctype="multipart/form-data"') !== false && 
            strpos($dashboard_content, 'enctype="multipart/form-data"') !== false,
        'Оба вызывают processDepositRequest' => 
            strpos($test_content, 'processDepositRequest') !== false && 
            strpos($dashboard_content, 'processDepositRequest') !== false,
        'Оба обрабатывают $_FILES' => 
            strpos($test_content, '$_FILES[') !== false && 
            strpos($dashboard_content, '$_FILES[') !== false,
        'Оба имеют CSRF защиту' => 
            strpos($test_content, 'csrf_token') !== false && 
            strpos($dashboard_content, 'csrf_token') !== false
    ];
    
    foreach ($comparison_checks as $check => $result) {
        $color = $result ? 'green' : 'red';
        $icon = $result ? 'check' : 'times';
        echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
        
        if (!$result) {
            $all_tests_passed = false;
        }
    }
} else {
    echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Один из файлов для сравнения не найден</p>";
    $all_tests_passed = false;
}

echo "</div>";

// Test 3: Check system functions
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>⚙️ Тест 3: Проверка системных функций</h2>";

$function_checks = [
    'uploadTransactionScreenshot существует' => function_exists('uploadTransactionScreenshot'),
    'processDepositRequest существует' => function_exists('processDepositRequest'),
    'generateCSRFToken существует' => function_exists('generateCSRFToken'),
    'verifyCSRFToken существует' => function_exists('verifyCSRFToken'),
    'Директория uploads/transactions/ существует' => is_dir('uploads/transactions/'),
    'Директория uploads/transactions/ доступна для записи' => is_writable('uploads/transactions/')
];

foreach ($function_checks as $check => $result) {
    $color = $result ? 'green' : 'red';
    $icon = $result ? 'check' : 'times';
    echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
    
    if (!$result) {
        $all_tests_passed = false;
    }
}

echo "</div>";

// Test 4: Database connectivity
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🗄️ Тест 4: Подключение к базе данных</h2>";

try {
    $db = getDB();
    echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Подключение к БД успешно</p>";
    
    // Check if transactions table exists and has correct structure
    $stmt = $db->query("DESCRIBE transactions");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_columns = ['id', 'user_id', 'type', 'amount', 'screenshot_path', 'status', 'created_at'];
    $missing_columns = array_diff($required_columns, $columns);
    
    if (empty($missing_columns)) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Таблица transactions имеет правильную структуру</p>";
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Отсутствуют колонки в таблице transactions: " . implode(', ', $missing_columns) . "</p>";
        $all_tests_passed = false;
    }
    
    // Test if we can insert a test record
    $stmt = $db->prepare("SELECT COUNT(*) FROM transactions WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $count = $stmt->fetchColumn();
    
    echo "<p class='text-blue-600 mb-2'><i class='fas fa-info mr-2'></i>Транзакций пользователя в БД: $count</p>";
    
} catch (Exception $e) {
    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка БД: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
}

echo "</div>";

// Test 5: File upload simulation
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📤 Тест 5: Симуляция загрузки файла</h2>";

// Create a test image
$test_image_data = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
$test_file_path = 'test_upload_simulation.png';
file_put_contents($test_file_path, $test_image_data);

$test_file = [
    'name' => 'test_upload_simulation.png',
    'type' => 'image/png',
    'size' => strlen($test_image_data),
    'tmp_name' => $test_file_path,
    'error' => 0
];

// Test uploadTransactionScreenshot function
if (function_exists('uploadTransactionScreenshot')) {
    $upload_result = uploadTransactionScreenshot($test_file);
    
    if ($upload_result['success']) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Функция uploadTransactionScreenshot работает</p>";
        
        $uploaded_file = $upload_result['filepath'];
        if (file_exists($uploaded_file)) {
            echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Файл успешно сохранен: $uploaded_file</p>";
            
            // Clean up
            unlink($uploaded_file);
        } else {
            echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Файл не найден после загрузки</p>";
            $all_tests_passed = false;
        }
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка функции uploadTransactionScreenshot: " . $upload_result['message'] . "</p>";
        $all_tests_passed = false;
    }
} else {
    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Функция uploadTransactionScreenshot не найдена</p>";
    $all_tests_passed = false;
}

// Clean up test file
if (file_exists($test_file_path)) {
    unlink($test_file_path);
}

echo "</div>";

// Final Results
if ($all_tests_passed) {
    echo "<div class='bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl p-8 text-white text-center'>
            <div class='text-6xl mb-4'>
                <i class='fas fa-check-circle'></i>
            </div>
            <h2 class='text-3xl font-bold mb-4'>🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!</h2>
            <p class='text-xl mb-6 opacity-90'>Dashboard.php исправлен и готов к использованию</p>
            
            <div class='bg-white bg-opacity-20 rounded-lg p-6 mb-6'>
                <h3 class='text-xl font-bold mb-3'>✅ Исправления применены:</h3>
                <ul class='text-left space-y-2 opacity-90'>
                    <li>• Добавлена расширенная отладка загрузки файлов</li>
                    <li>• Добавлена валидация формы перед отправкой</li>
                    <li>• Улучшено модальное окно (style.display вместо hidden)</li>
                    <li>• Добавлены console.log для отладки в браузере</li>
                    <li>• Проверена совместимость с унифицированной системой хранения</li>
                </ul>
            </div>
            
            <div class='flex flex-wrap justify-center gap-4'>
                <a href='pages/dashboard.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-tachometer-alt mr-2'></i>Тест Dashboard
                </a>
                <a href='admin/transactions.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-cog mr-2'></i>Админ-панель
                </a>
                <a href='test_unified_system.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-check-double mr-2'></i>Тест системы
                </a>
                <a href='unified_system_report.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-chart-line mr-2'></i>Отчет системы
                </a>
            </div>
          </div>";
} else {
    echo "<div class='bg-red-50 border-2 border-red-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-red-500 mb-4'>
                <i class='fas fa-exclamation-triangle'></i>
            </div>
            <h2 class='text-3xl font-bold text-red-900 mb-4'>⚠️ Некоторые тесты не прошли</h2>
            <p class='text-red-700 text-lg mb-6'>Требуется дополнительная диагностика</p>
            
            <div class='flex flex-wrap justify-center gap-4'>
                <a href='diagnose_dashboard_upload.php' class='bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-search mr-2'></i>Диагностика
                </a>
                <a href='fix_dashboard_upload.php' class='bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-wrench mr-2'></i>Повторить исправления
                </a>
                <a href='pages/simple_deposit_form.php' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-test-tube mr-2'></i>Простая форма
                </a>
            </div>
          </div>";
}

echo "</div></body></html>";
?>
