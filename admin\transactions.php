<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../pages/login.php');
}

// Handle transaction actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', 'Недействительный токен безопасности');
    } else {
        $action = $_POST['action'] ?? '';
        $transaction_id = intval($_POST['transaction_id'] ?? 0);
        
        if ($action === 'approve' && $transaction_id) {
            $admin_notes = sanitizeInput($_POST['admin_notes'] ?? '');
            
            $db = getDB();
            $stmt = $db->prepare("SELECT * FROM transactions WHERE id = ?");
            $stmt->execute([$transaction_id]);
            $transaction = $stmt->fetch();
            
            if ($transaction && $transaction['status'] === 'pending') {
                // Update transaction status
                $stmt = $db->prepare("UPDATE transactions SET status = 'approved', admin_notes = ?, processed_at = NOW() WHERE id = ?");
                if ($stmt->execute([$admin_notes, $transaction_id])) {
                    // If it's a deposit, add to user balance
                    if ($transaction['type'] === 'deposit') {
                        $stmt = $db->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
                        $stmt->execute([$transaction['amount'], $transaction['user_id']]);
                    }
                    setFlashMessage('success', 'Транзакция одобрена');
                } else {
                    setFlashMessage('error', 'Ошибка при одобрении транзакции');
                }
            }
        } elseif ($action === 'reject' && $transaction_id) {
            $admin_notes = sanitizeInput($_POST['admin_notes'] ?? '');
            
            $db = getDB();
            $stmt = $db->prepare("UPDATE transactions SET status = 'rejected', admin_notes = ?, processed_at = NOW() WHERE id = ?");
            if ($stmt->execute([$admin_notes, $transaction_id])) {
                setFlashMessage('success', 'Транзакция отклонена');
            } else {
                setFlashMessage('error', 'Ошибка при отклонении транзакции');
            }
        }
    }
}

// Get transactions
$db = getDB();
$filter = $_GET['filter'] ?? 'all';
$type_filter = $_GET['type'] ?? 'all';

$sql = "SELECT t.*, u.username, u.email FROM transactions t JOIN users u ON t.user_id = u.id WHERE 1=1";
$params = [];

if ($filter === 'pending') {
    $sql .= " AND t.status = 'pending'";
} elseif ($filter === 'approved') {
    $sql .= " AND t.status = 'approved'";
} elseif ($filter === 'rejected') {
    $sql .= " AND t.status = 'rejected'";
}

if ($type_filter === 'deposit') {
    $sql .= " AND t.type = 'deposit'";
} elseif ($type_filter === 'withdrawal') {
    $sql .= " AND t.type = 'withdrawal'";
}

$sql .= " ORDER BY t.created_at DESC";

$stmt = $db->prepare($sql);
$stmt->execute($params);
$transactions = $stmt->fetchAll();

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Управление транзакциями</h1>
                <p class="text-gray-600 mt-2">Просмотр и обработка всех транзакций пользователей</p>
            </div>
            <a href="index.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300">
                <i class="fas fa-arrow-left mr-2"></i>Назад к панели
            </a>
        </div>

        <!-- Flash Messages -->
        <?php
        $success_message = getFlashMessage('success');
        $error_message = getFlashMessage('error');
        ?>

        <?php if ($success_message): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                <i class="fas fa-check-circle mr-2"></i><?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- Filters -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100 p-6 mb-6">
            <div class="flex flex-wrap gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Статус</label>
                    <select onchange="updateFilter('filter', this.value)" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>Все</option>
                        <option value="pending" <?php echo $filter === 'pending' ? 'selected' : ''; ?>>Ожидающие</option>
                        <option value="approved" <?php echo $filter === 'approved' ? 'selected' : ''; ?>>Одобренные</option>
                        <option value="rejected" <?php echo $filter === 'rejected' ? 'selected' : ''; ?>>Отклоненные</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Тип</label>
                    <select onchange="updateFilter('type', this.value)" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="all" <?php echo $type_filter === 'all' ? 'selected' : ''; ?>>Все</option>
                        <option value="deposit" <?php echo $type_filter === 'deposit' ? 'selected' : ''; ?>>Депозиты</option>
                        <option value="withdrawal" <?php echo $type_filter === 'withdrawal' ? 'selected' : ''; ?>>Выводы</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Транзакции (<?php echo count($transactions); ?>)</h2>
            </div>
            
            <?php if (empty($transactions)): ?>
                <div class="p-8 text-center">
                    <i class="fas fa-receipt text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-600">Транзакции не найдены</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Пользователь</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Тип</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Сумма</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Статус</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Скриншот</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Дата</th>
                                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <?php foreach ($transactions as $transaction): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="py-4 px-4">
                                        <div>
                                            <div class="font-semibold text-gray-900"><?php echo htmlspecialchars($transaction['username']); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($transaction['email']); ?></div>
                                        </div>
                                    </td>
                                    <td class="py-4 px-4">
                                        <span class="px-2 py-1 rounded-full text-xs font-semibold <?php 
                                            echo $transaction['type'] === 'deposit' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; 
                                        ?>">
                                            <?php echo $transaction['type'] === 'deposit' ? 'Депозит' : 'Вывод'; ?>
                                        </span>
                                    </td>
                                    <td class="py-4 px-4">
                                        <span class="font-semibold text-gray-900"><?php echo formatCurrency($transaction['amount']); ?></span>
                                    </td>
                                    <td class="py-4 px-4">
                                        <span class="px-2 py-1 rounded-full text-xs font-semibold <?php 
                                            if ($transaction['status'] === 'approved') {
                                                echo 'bg-green-100 text-green-800';
                                            } elseif ($transaction['status'] === 'rejected') {
                                                echo 'bg-red-100 text-red-800';
                                            } else {
                                                echo 'bg-yellow-100 text-yellow-800';
                                            }
                                        ?>">
                                            <?php 
                                            if ($transaction['status'] === 'approved') {
                                                echo 'Одобрено';
                                            } elseif ($transaction['status'] === 'rejected') {
                                                echo 'Отклонено';
                                            } else {
                                                echo 'Ожидает';
                                            }
                                            ?>
                                        </span>
                                    </td>
                                    <td class="py-4 px-4">
                                        <?php if (!empty($transaction['screenshot_path'])): ?>
                                            <button onclick="viewScreenshot('<?php echo htmlspecialchars($transaction['screenshot_path']); ?>')" 
                                                    class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors">
                                                <i class="fas fa-image mr-1"></i>Просмотр
                                            </button>
                                        <?php else: ?>
                                            <span class="text-gray-400 text-sm">Нет скриншота</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="py-4 px-4">
                                        <span class="text-sm text-gray-600"><?php echo date('d.m.Y H:i', strtotime($transaction['created_at'])); ?></span>
                                    </td>
                                    <td class="py-4 px-4 text-center">
                                        <?php if ($transaction['status'] === 'pending'): ?>
                                            <div class="flex justify-center space-x-2">
                                                <button onclick="openActionModal(<?php echo $transaction['id']; ?>, 'approve', '<?php echo htmlspecialchars($transaction['username']); ?>', '<?php echo formatCurrency($transaction['amount']); ?>')" 
                                                        class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600 transition-colors">
                                                    <i class="fas fa-check mr-1"></i>Одобрить
                                                </button>
                                                <button onclick="openActionModal(<?php echo $transaction['id']; ?>, 'reject', '<?php echo htmlspecialchars($transaction['username']); ?>', '<?php echo formatCurrency($transaction['amount']); ?>')" 
                                                        class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600 transition-colors">
                                                    <i class="fas fa-times mr-1"></i>Отклонить
                                                </button>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-gray-400 text-sm">Обработано</span>
                                            <?php if (!empty($transaction['admin_notes'])): ?>
                                                <br><small class="text-gray-500"><?php echo htmlspecialchars($transaction['admin_notes']); ?></small>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Screenshot Modal -->
<div id="screenshotModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-4xl w-full max-h-screen overflow-auto">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 class="text-xl font-bold text-gray-900">Скриншот транзакции</h3>
                <button onclick="closeScreenshotModal()" class="text-gray-500 hover:text-gray-700 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="p-6">
                <img id="screenshotImage" src="" alt="Скриншот транзакции" class="w-full h-auto rounded-lg shadow-lg">
            </div>
        </div>
    </div>
</div>

<!-- Action Modal -->
<div id="actionModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6 border border-gray-100 shadow-2xl">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-900" id="actionModalTitle">Действие с транзакцией</h3>
                <button onclick="closeActionModal()" class="text-gray-500 hover:text-gray-700 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div id="actionModalContent" class="mb-6">
                <!-- Content will be populated by JavaScript -->
            </div>

            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="transaction_id" id="actionTransactionId">
                <input type="hidden" name="action" id="actionType">

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Комментарий администратора</label>
                    <textarea name="admin_notes" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Добавьте комментарий (необязательно)"></textarea>
                </div>

                <div class="flex space-x-3">
                    <button type="button" onclick="closeActionModal()"
                            class="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                        Отмена
                    </button>
                    <button type="submit" id="actionSubmitBtn" class="flex-1 py-2 rounded-lg font-medium transition-all duration-300 shadow-lg">
                        Подтвердить
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function updateFilter(param, value) {
    const url = new URL(window.location);
    url.searchParams.set(param, value);
    window.location = url;
}

function viewScreenshot(imagePath) {
    document.getElementById('screenshotImage').src = imagePath;
    document.getElementById('screenshotModal').classList.remove('hidden');
}

function closeScreenshotModal() {
    document.getElementById('screenshotModal').classList.add('hidden');
}

function openActionModal(transactionId, action, username, amount) {
    document.getElementById('actionTransactionId').value = transactionId;
    document.getElementById('actionType').value = action;

    const title = action === 'approve' ? 'Одобрить транзакцию' : 'Отклонить транзакцию';
    const actionText = action === 'approve' ? 'одобрить' : 'отклонить';
    const buttonClass = action === 'approve' ?
        'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white' :
        'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white';

    document.getElementById('actionModalTitle').textContent = title;
    document.getElementById('actionModalContent').innerHTML = `
        <div class="text-center">
            <p class="text-gray-600 mb-2">Вы уверены, что хотите ${actionText} эту транзакцию?</p>
            <p class="font-semibold text-gray-900">Пользователь: ${username}</p>
            <p class="font-semibold text-gray-900">Сумма: ${amount}</p>
        </div>
    `;

    const submitBtn = document.getElementById('actionSubmitBtn');
    submitBtn.className = `flex-1 py-2 rounded-lg font-medium transition-all duration-300 shadow-lg ${buttonClass}`;
    submitBtn.textContent = action === 'approve' ? 'Одобрить' : 'Отклонить';

    document.getElementById('actionModal').classList.remove('hidden');
}

function closeActionModal() {
    document.getElementById('actionModal').classList.add('hidden');
}

// Close modals when clicking outside
document.getElementById('screenshotModal').addEventListener('click', function(e) {
    if (e.target === this) closeScreenshotModal();
});

document.getElementById('actionModal').addEventListener('click', function(e) {
    if (e.target === this) closeActionModal();
});
</script>

<?php include '../includes/footer.php'; ?>
