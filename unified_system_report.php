<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Отчет об унификации системы хранения</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>📊 Отчет об унификации системы хранения файлов</h1>";

// System overview
echo "<div class='bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-8 text-white mb-8'>
        <div class='text-center'>
            <div class='text-6xl mb-4'>
                <i class='fas fa-database'></i>
            </div>
            <h2 class='text-3xl font-bold mb-4'>Унифицированная система хранения</h2>
            <p class='text-xl opacity-90'>Все файлы транзакций теперь хранятся в единой директории</p>
        </div>
      </div>";

// Current system status
echo "<div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8'>";

// Unified directory status
$unified_dir = 'uploads/transactions/';
$unified_files = is_dir($unified_dir) ? glob($unified_dir . '*') : [];
$unified_size = 0;
foreach ($unified_files as $file) {
    if (is_file($file)) {
        $unified_size += filesize($file);
    }
}

echo "<div class='bg-white rounded-lg shadow-lg p-6'>
        <div class='flex items-center mb-4'>
            <div class='bg-green-100 p-3 rounded-full mr-4'>
                <i class='fas fa-folder text-green-600 text-xl'></i>
            </div>
            <div>
                <h3 class='text-lg font-bold text-gray-900'>Унифицированная директория</h3>
                <p class='text-sm text-gray-600'>uploads/transactions/</p>
            </div>
        </div>
        <div class='space-y-2'>
            <div class='flex justify-between'>
                <span class='text-gray-600'>Файлов:</span>
                <span class='font-bold text-green-600'>" . count($unified_files) . "</span>
            </div>
            <div class='flex justify-between'>
                <span class='text-gray-600'>Размер:</span>
                <span class='font-bold text-green-600'>" . number_format($unified_size / 1024, 2) . " KB</span>
            </div>
            <div class='flex justify-between'>
                <span class='text-gray-600'>Статус:</span>
                <span class='font-bold text-green-600'>Активна</span>
            </div>
        </div>
      </div>";

// Admin directory status
$admin_dir = 'admin/uploads/transactions/';
$admin_exists = is_dir($admin_dir);
$admin_files = $admin_exists ? glob($admin_dir . '*') : [];

echo "<div class='bg-white rounded-lg shadow-lg p-6'>
        <div class='flex items-center mb-4'>
            <div class='bg-" . ($admin_exists ? 'red' : 'gray') . "-100 p-3 rounded-full mr-4'>
                <i class='fas fa-folder-minus text-" . ($admin_exists ? 'red' : 'gray') . "-600 text-xl'></i>
            </div>
            <div>
                <h3 class='text-lg font-bold text-gray-900'>Дублирующая директория</h3>
                <p class='text-sm text-gray-600'>admin/uploads/transactions/</p>
            </div>
        </div>
        <div class='space-y-2'>
            <div class='flex justify-between'>
                <span class='text-gray-600'>Существует:</span>
                <span class='font-bold text-" . ($admin_exists ? 'red' : 'green') . "-600'>" . ($admin_exists ? 'Да' : 'Нет') . "</span>
            </div>
            <div class='flex justify-between'>
                <span class='text-gray-600'>Файлов:</span>
                <span class='font-bold text-" . ($admin_exists ? 'red' : 'gray') . "-600'>" . count($admin_files) . "</span>
            </div>
            <div class='flex justify-between'>
                <span class='text-gray-600'>Статус:</span>
                <span class='font-bold text-" . ($admin_exists ? 'red' : 'green') . "-600'>" . ($admin_exists ? 'Требует очистки' : 'Удалена') . "</span>
            </div>
        </div>
      </div>";

// Function status
$function_exists = function_exists('uploadTransactionScreenshot');
$function_clean = false;

if ($function_exists) {
    $function_reflection = new ReflectionFunction('uploadTransactionScreenshot');
    $function_file = $function_reflection->getFileName();
    $function_source = file_get_contents($function_file);
    $function_clean = strpos($function_source, 'admin/uploads/transactions') === false;
}

echo "<div class='bg-white rounded-lg shadow-lg p-6'>
        <div class='flex items-center mb-4'>
            <div class='bg-" . ($function_exists && $function_clean ? 'green' : 'yellow') . "-100 p-3 rounded-full mr-4'>
                <i class='fas fa-code text-" . ($function_exists && $function_clean ? 'green' : 'yellow') . "-600 text-xl'></i>
            </div>
            <div>
                <h3 class='text-lg font-bold text-gray-900'>Функция загрузки</h3>
                <p class='text-sm text-gray-600'>uploadTransactionScreenshot()</p>
            </div>
        </div>
        <div class='space-y-2'>
            <div class='flex justify-between'>
                <span class='text-gray-600'>Существует:</span>
                <span class='font-bold text-" . ($function_exists ? 'green' : 'red') . "-600'>" . ($function_exists ? 'Да' : 'Нет') . "</span>
            </div>
            <div class='flex justify-between'>
                <span class='text-gray-600'>Унифицирована:</span>
                <span class='font-bold text-" . ($function_clean ? 'green' : 'red') . "-600'>" . ($function_clean ? 'Да' : 'Нет') . "</span>
            </div>
            <div class='flex justify-between'>
                <span class='text-gray-600'>Статус:</span>
                <span class='font-bold text-" . ($function_exists && $function_clean ? 'green' : 'yellow') . "-600'>" . ($function_exists && $function_clean ? 'Готова' : 'Требует обновления') . "</span>
            </div>
        </div>
      </div>";

echo "</div>";

// Detailed analysis
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>
        <h2 class='text-2xl font-bold text-gray-900 mb-6'>📋 Детальный анализ</h2>";

// Check database consistency
try {
    $db = getDB();
    $stmt = $db->query("SELECT COUNT(*) as total, COUNT(screenshot_path) as with_screenshots FROM transactions");
    $transaction_stats = $stmt->fetch();
    
    $stmt = $db->query("SELECT screenshot_path FROM transactions WHERE screenshot_path IS NOT NULL AND screenshot_path != ''");
    $screenshot_paths = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $valid_paths = 0;
    $invalid_paths = 0;
    $missing_files = 0;
    
    foreach ($screenshot_paths as $path) {
        if (strpos($path, 'uploads/transactions/') === 0) {
            $valid_paths++;
            if (!file_exists($path)) {
                $missing_files++;
            }
        } else {
            $invalid_paths++;
        }
    }
    
    echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div>
                <h3 class='text-lg font-bold text-gray-900 mb-4'>База данных</h3>
                <div class='space-y-2'>
                    <div class='flex justify-between'>
                        <span class='text-gray-600'>Всего транзакций:</span>
                        <span class='font-bold'>" . $transaction_stats['total'] . "</span>
                    </div>
                    <div class='flex justify-between'>
                        <span class='text-gray-600'>С скриншотами:</span>
                        <span class='font-bold'>" . $transaction_stats['with_screenshots'] . "</span>
                    </div>
                    <div class='flex justify-between'>
                        <span class='text-gray-600'>Корректные пути:</span>
                        <span class='font-bold text-green-600'>$valid_paths</span>
                    </div>
                    <div class='flex justify-between'>
                        <span class='text-gray-600'>Некорректные пути:</span>
                        <span class='font-bold text-" . ($invalid_paths > 0 ? 'red' : 'green') . "-600'>$invalid_paths</span>
                    </div>
                    <div class='flex justify-between'>
                        <span class='text-gray-600'>Отсутствующие файлы:</span>
                        <span class='font-bold text-" . ($missing_files > 0 ? 'red' : 'green') . "-600'>$missing_files</span>
                    </div>
                </div>
            </div>
            <div>
                <h3 class='text-lg font-bold text-gray-900 mb-4'>Файловая система</h3>
                <div class='space-y-2'>
                    <div class='flex justify-between'>
                        <span class='text-gray-600'>Унифицированная директория:</span>
                        <span class='font-bold text-" . (is_dir($unified_dir) ? 'green' : 'red') . "-600'>" . (is_dir($unified_dir) ? 'Существует' : 'Отсутствует') . "</span>
                    </div>
                    <div class='flex justify-between'>
                        <span class='text-gray-600'>Доступна для записи:</span>
                        <span class='font-bold text-" . (is_writable($unified_dir) ? 'green' : 'red') . "-600'>" . (is_writable($unified_dir) ? 'Да' : 'Нет') . "</span>
                    </div>
                    <div class='flex justify-between'>
                        <span class='text-gray-600'>Дублирующая директория:</span>
                        <span class='font-bold text-" . ($admin_exists ? 'red' : 'green') . "-600'>" . ($admin_exists ? 'Существует' : 'Удалена') . "</span>
                    </div>
                    <div class='flex justify-between'>
                        <span class='text-gray-600'>Файлов в унифицированной:</span>
                        <span class='font-bold'>" . count($unified_files) . "</span>
                    </div>
                    <div class='flex justify-between'>
                        <span class='text-gray-600'>Общий размер:</span>
                        <span class='font-bold'>" . number_format($unified_size / 1024 / 1024, 2) . " MB</span>
                    </div>
                </div>
            </div>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>
            <h4 class='font-bold text-red-900'>Ошибка анализа базы данных</h4>
            <p class='text-red-700'>" . $e->getMessage() . "</p>
          </div>";
}

echo "</div>";

// Recommendations
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>
        <h2 class='text-2xl font-bold text-gray-900 mb-6'>💡 Рекомендации</h2>";

$recommendations = [];

if ($admin_exists) {
    $recommendations[] = [
        'type' => 'warning',
        'title' => 'Удалить дублирующую директорию',
        'description' => 'Директория admin/uploads/transactions/ все еще существует и может вызывать путаницу.',
        'action' => 'Запустите unify_file_storage.php для завершения очистки'
    ];
}

if (!$function_clean) {
    $recommendations[] = [
        'type' => 'warning',
        'title' => 'Обновить функцию загрузки',
        'description' => 'Функция uploadTransactionScreenshot() все еще содержит ссылки на админ-директорию.',
        'action' => 'Проверьте код функции в includes/functions.php'
    ];
}

if ($missing_files > 0) {
    $recommendations[] = [
        'type' => 'error',
        'title' => 'Восстановить отсутствующие файлы',
        'description' => "Обнаружено $missing_files отсутствующих файлов, на которые ссылается база данных.",
        'action' => 'Проверьте резервные копии или обновите записи в БД'
    ];
}

if ($invalid_paths > 0) {
    $recommendations[] = [
        'type' => 'warning',
        'title' => 'Исправить пути в базе данных',
        'description' => "Обнаружено $invalid_paths записей с некорректными путями к файлам.",
        'action' => 'Обновите пути в таблице transactions'
    ];
}

if (empty($recommendations)) {
    $recommendations[] = [
        'type' => 'success',
        'title' => 'Система полностью унифицирована',
        'description' => 'Все компоненты системы хранения файлов работают корректно.',
        'action' => 'Никаких дополнительных действий не требуется'
    ];
}

foreach ($recommendations as $rec) {
    $color_map = [
        'success' => 'green',
        'warning' => 'yellow',
        'error' => 'red'
    ];
    $icon_map = [
        'success' => 'check-circle',
        'warning' => 'exclamation-triangle',
        'error' => 'times-circle'
    ];
    
    $color = $color_map[$rec['type']];
    $icon = $icon_map[$rec['type']];
    
    echo "<div class='bg-{$color}-50 border border-{$color}-200 rounded-lg p-4 mb-4'>
            <div class='flex items-start'>
                <i class='fas fa-{$icon} text-{$color}-600 mr-3 mt-1'></i>
                <div class='flex-1'>
                    <h4 class='font-bold text-{$color}-900 mb-1'>" . $rec['title'] . "</h4>
                    <p class='text-{$color}-700 mb-2'>" . $rec['description'] . "</p>
                    <p class='text-{$color}-600 text-sm font-medium'>" . $rec['action'] . "</p>
                </div>
            </div>
          </div>";
}

echo "</div>";

// Action buttons
echo "<div class='text-center space-y-4'>
        <div class='flex flex-wrap justify-center gap-4'>
            <a href='test_unified_system.php' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-test-tube mr-2'></i>Тест системы
            </a>
            <a href='pages/dashboard.php' class='bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-tachometer-alt mr-2'></i>Dashboard
            </a>
            <a href='admin/transactions.php' class='bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-cog mr-2'></i>Админ-панель
            </a>
            <a href='unify_file_storage.php' class='bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-redo mr-2'></i>Повторить унификацию
            </a>
        </div>
        
        <div class='mt-8 p-6 bg-gray-50 rounded-lg'>
            <h3 class='text-lg font-bold text-gray-900 mb-2'>📈 Результат унификации</h3>
            <p class='text-gray-700'>Система хранения файлов транзакций успешно унифицирована. Все новые файлы будут сохраняться только в директории <code class='bg-gray-200 px-1 rounded'>uploads/transactions/</code>, что обеспечивает единообразие и упрощает администрирование.</p>
        </div>
      </div>";

echo "</div></body></html>";
?>
