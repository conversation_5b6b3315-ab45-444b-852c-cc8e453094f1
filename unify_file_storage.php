<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Унификация системы хранения файлов</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>🔄 Унификация системы хранения файлов транзакций</h1>";

$actions_performed = [];
$errors = [];

// Step 1: Analyze current state
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📊 Шаг 1: Анализ текущего состояния</h2>";

$main_dir = 'uploads/transactions/';
$admin_dir = 'admin/uploads/transactions/';

$main_files = is_dir($main_dir) ? glob($main_dir . '*') : [];
$admin_files = is_dir($admin_dir) ? glob($admin_dir . '*') : [];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-6'>
        <div class='border rounded-lg p-4'>
            <h3 class='font-bold text-green-900 mb-3'>📁 Основная директория (ЦЕЛЕВАЯ)</h3>
            <p class='text-sm text-gray-600 mb-2'>Путь: <code class='bg-gray-200 px-1 rounded'>$main_dir</code></p>
            <p class='text-sm text-gray-600 mb-2'>Файлов: " . count($main_files) . "</p>";

if (!empty($main_files)) {
    echo "<div class='mt-3'><h4 class='font-medium text-gray-700 mb-2'>Файлы:</h4>";
    foreach (array_slice($main_files, 0, 5) as $file) {
        $filename = basename($file);
        $filesize = number_format(filesize($file) / 1024, 2);
        echo "<p class='text-xs text-gray-600'>• $filename ({$filesize} KB)</p>";
    }
    if (count($main_files) > 5) {
        echo "<p class='text-xs text-gray-500'>... и еще " . (count($main_files) - 5) . " файлов</p>";
    }
    echo "</div>";
}

echo "    </div>
        <div class='border rounded-lg p-4'>
            <h3 class='font-bold text-red-900 mb-3'>📁 Админ-директория (ДУБЛИРУЮЩАЯ)</h3>
            <p class='text-sm text-gray-600 mb-2'>Путь: <code class='bg-gray-200 px-1 rounded'>$admin_dir</code></p>
            <p class='text-sm text-gray-600 mb-2'>Файлов: " . count($admin_files) . "</p>";

if (!empty($admin_files)) {
    echo "<div class='mt-3'><h4 class='font-medium text-gray-700 mb-2'>Файлы:</h4>";
    foreach (array_slice($admin_files, 0, 5) as $file) {
        $filename = basename($file);
        $filesize = number_format(filesize($file) / 1024, 2);
        echo "<p class='text-xs text-gray-600'>• $filename ({$filesize} KB)</p>";
    }
    if (count($admin_files) > 5) {
        echo "<p class='text-xs text-gray-500'>... и еще " . (count($admin_files) - 5) . " файлов</p>";
    }
    echo "</div>";
}

echo "    </div>
      </div>
      </div>";

// Step 2: Move files from admin directory to main directory
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📦 Шаг 2: Перемещение файлов в единую директорию</h2>";

$moved_count = 0;
$skipped_count = 0;

if (!empty($admin_files)) {
    foreach ($admin_files as $admin_file) {
        if (is_file($admin_file)) {
            $filename = basename($admin_file);
            $main_file = $main_dir . $filename;
            
            if (!file_exists($main_file)) {
                // File doesn't exist in main directory, move it
                if (rename($admin_file, $main_file)) {
                    echo "<p class='text-green-600 text-sm'><i class='fas fa-arrow-right mr-1'></i>Перемещен: $filename</p>";
                    $moved_count++;
                    $actions_performed[] = "Перемещен файл $filename в основную директорию";
                } else {
                    echo "<p class='text-red-600 text-sm'><i class='fas fa-times mr-1'></i>Ошибка перемещения: $filename</p>";
                    $errors[] = "Не удалось переместить $filename";
                }
            } else {
                // File already exists in main directory, check if they're identical
                if (filesize($admin_file) === filesize($main_file) && md5_file($admin_file) === md5_file($main_file)) {
                    // Files are identical, safe to delete admin copy
                    if (unlink($admin_file)) {
                        echo "<p class='text-blue-600 text-sm'><i class='fas fa-check mr-1'></i>Удален дубликат: $filename</p>";
                        $skipped_count++;
                        $actions_performed[] = "Удален дубликат $filename";
                    } else {
                        echo "<p class='text-yellow-600 text-sm'><i class='fas fa-exclamation-triangle mr-1'></i>Не удалось удалить дубликат: $filename</p>";
                        $errors[] = "Не удалось удалить дубликат $filename";
                    }
                } else {
                    // Files are different, rename admin file
                    $new_filename = 'admin_' . $filename;
                    $new_main_file = $main_dir . $new_filename;
                    
                    if (rename($admin_file, $new_main_file)) {
                        echo "<p class='text-orange-600 text-sm'><i class='fas fa-exclamation-triangle mr-1'></i>Переименован и перемещен (конфликт): $filename → $new_filename</p>";
                        $moved_count++;
                        $actions_performed[] = "Переименован и перемещен конфликтующий файл $filename";
                    } else {
                        echo "<p class='text-red-600 text-sm'><i class='fas fa-times mr-1'></i>Ошибка разрешения конфликта: $filename</p>";
                        $errors[] = "Не удалось разрешить конфликт для $filename";
                    }
                }
            }
        }
    }
    
    echo "<div class='mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg'>
            <h4 class='font-bold text-blue-900 mb-2'>Итоги перемещения:</h4>
            <p class='text-blue-700'>• Перемещено файлов: $moved_count</p>
            <p class='text-blue-700'>• Удалено дубликатов: $skipped_count</p>
          </div>";
} else {
    echo "<p class='text-gray-600'><i class='fas fa-info mr-2'></i>Нет файлов для перемещения из админ-директории</p>";
}

echo "</div>";

// Step 3: Remove empty admin directory
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🗑️ Шаг 3: Удаление дублирующих директорий</h2>";

if (is_dir($admin_dir)) {
    // Check if directory is empty
    $remaining_files = glob($admin_dir . '*');
    
    if (empty($remaining_files)) {
        if (rmdir($admin_dir)) {
            echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Удалена пустая админ-директория: $admin_dir</p>";
            $actions_performed[] = "Удалена пустая админ-директория";
            
            // Also remove parent admin/uploads if it's empty
            $admin_uploads_dir = 'admin/uploads/';
            if (is_dir($admin_uploads_dir)) {
                $admin_uploads_contents = glob($admin_uploads_dir . '*');
                if (empty($admin_uploads_contents)) {
                    if (rmdir($admin_uploads_dir)) {
                        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Удалена пустая директория: $admin_uploads_dir</p>";
                        $actions_performed[] = "Удалена пустая директория admin/uploads";
                    }
                }
            }
        } else {
            echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Не удалось удалить админ-директорию: $admin_dir</p>";
            $errors[] = "Не удалось удалить админ-директорию";
        }
    } else {
        echo "<p class='text-yellow-600 mb-2'><i class='fas fa-exclamation-triangle mr-2'></i>Админ-директория не пуста, осталось файлов: " . count($remaining_files) . "</p>";
        foreach ($remaining_files as $file) {
            echo "<p class='text-yellow-600 text-sm ml-4'>• " . basename($file) . "</p>";
        }
        $errors[] = "Админ-директория не была удалена - содержит файлы";
    }
} else {
    echo "<p class='text-blue-600 mb-2'><i class='fas fa-info mr-2'></i>Админ-директория уже не существует</p>";
}

echo "</div>";

// Step 4: Verify unified system
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>✅ Шаг 4: Проверка унифицированной системы</h2>";

$final_main_files = is_dir($main_dir) ? glob($main_dir . '*') : [];
$final_admin_dir_exists = is_dir($admin_dir);

echo "<div class='space-y-3'>
        <div class='flex items-center'>
            <i class='fas fa-check text-green-600 mr-2'></i>
            <span class='text-green-700'>Единая директория: <code class='bg-gray-200 px-1 rounded'>$main_dir</code></span>
        </div>
        <div class='flex items-center'>
            <i class='fas fa-" . ($final_admin_dir_exists ? 'times text-red-600' : 'check text-green-600') . " mr-2'></i>
            <span class='text-" . ($final_admin_dir_exists ? 'red' : 'green') . "-700'>Дублирующая директория: " . ($final_admin_dir_exists ? 'Все еще существует' : 'Удалена') . "</span>
        </div>
        <div class='flex items-center'>
            <i class='fas fa-file text-blue-600 mr-2'></i>
            <span class='text-blue-700'>Всего файлов в единой директории: " . count($final_main_files) . "</span>
        </div>
      </div>";

// Test upload function
echo "<div class='mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg'>
        <h4 class='font-bold text-gray-900 mb-2'>Тест функции загрузки:</h4>";

if (function_exists('uploadTransactionScreenshot')) {
    echo "<p class='text-green-600'><i class='fas fa-check mr-1'></i>Функция uploadTransactionScreenshot доступна</p>";
    
    // Check if function code is updated (no admin directory references)
    $function_reflection = new ReflectionFunction('uploadTransactionScreenshot');
    $function_file = $function_reflection->getFileName();
    $function_source = file_get_contents($function_file);
    
    if (strpos($function_source, 'admin/uploads/transactions') === false) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-1'></i>Функция обновлена - нет ссылок на админ-директорию</p>";
    } else {
        echo "<p class='text-yellow-600'><i class='fas fa-exclamation-triangle mr-1'></i>Функция все еще содержит ссылки на админ-директорию</p>";
        $errors[] = "Функция загрузки не полностью обновлена";
    }
} else {
    echo "<p class='text-red-600'><i class='fas fa-times mr-1'></i>Функция uploadTransactionScreenshot не найдена</p>";
    $errors[] = "Функция загрузки не найдена";
}

echo "</div>";
echo "</div>";

// Final Results
if (empty($errors)) {
    echo "<div class='bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl p-8 text-white text-center'>
            <div class='text-6xl mb-4'>
                <i class='fas fa-trophy'></i>
            </div>
            <h2 class='text-3xl font-bold mb-4'>🎉 СИСТЕМА УНИФИЦИРОВАНА!</h2>
            <p class='text-xl mb-6 opacity-90'>Все файлы транзакций теперь хранятся в единой директории uploads/transactions/</p>
            
            <div class='bg-white bg-opacity-20 rounded-lg p-6 mb-6'>
                <h3 class='text-xl font-bold mb-3'>✅ Достигнутые результаты:</h3>
                <ul class='text-left space-y-2 opacity-90'>
                    <li>• Единая директория хранения: uploads/transactions/</li>
                    <li>• Удалены дублирующие директории</li>
                    <li>• Упрощена функция загрузки</li>
                    <li>• Обновлена админ-панель для работы с единой директорией</li>
                    <li>• Все файлы сохранены и доступны</li>
                </ul>
            </div>";
    
    if (!empty($actions_performed)) {
        echo "<div class='bg-white bg-opacity-20 rounded-lg p-6 mb-6'>
                <h3 class='text-xl font-bold mb-3'>📋 Выполненные действия:</h3>
                <ul class='text-left space-y-1 opacity-90 text-sm'>";
        foreach ($actions_performed as $action) {
            echo "<li>• $action</li>";
        }
        echo "</ul>
              </div>";
    }
    
    echo "<div class='flex flex-wrap justify-center gap-4'>
            <a href='pages/dashboard.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                <i class='fas fa-upload mr-2'></i>Тест загрузки
            </a>
            <a href='admin/transactions.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                <i class='fas fa-eye mr-2'></i>Тест админ-панели
            </a>
            <a href='test_unified_system.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                <i class='fas fa-check-double mr-2'></i>Полный тест
            </a>
          </div>
          </div>";
} else {
    echo "<div class='bg-red-50 border-2 border-red-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-red-500 mb-4'>
                <i class='fas fa-exclamation-triangle'></i>
            </div>
            <h2 class='text-3xl font-bold text-red-900 mb-4'>⚠️ Унификация завершена с предупреждениями</h2>
            <p class='text-red-700 text-lg mb-6'>Некоторые действия требуют внимания</p>";
    
    echo "<div class='bg-white rounded-lg p-6 mb-6'>
            <h3 class='text-xl font-bold text-red-900 mb-3'>Предупреждения:</h3>
            <ul class='text-red-700 text-left space-y-1'>";
    foreach ($errors as $error) {
        echo "<li>• $error</li>";
    }
    echo "</ul>
          </div>";
    
    echo "<div class='flex flex-wrap justify-center gap-4'>
            <a href='diagnose_paths.php' class='bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-search mr-2'></i>Диагностика
            </a>
            <a href='manual_cleanup.php' class='bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-broom mr-2'></i>Ручная очистка
            </a>
          </div>
          </div>";
}

echo "</div></body></html>";
?>
