<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../pages/login.php');
}

// Handle blog post operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        setFlashMessage('error', 'Invalid security token');
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'create' || $action === 'update') {
            $title = sanitizeInput($_POST['title'] ?? '');
            $content = $_POST['content'] ?? '';
            $excerpt = sanitizeInput($_POST['excerpt'] ?? '');
            $category = sanitizeInput($_POST['category'] ?? 'general');
            $tags = sanitizeInput($_POST['tags'] ?? '');
            $meta_title = sanitizeInput($_POST['meta_title'] ?? '');
            $meta_description = sanitizeInput($_POST['meta_description'] ?? '');
            $is_published = isset($_POST['is_published']) ? 1 : 0;
            
            // Generate slug from title
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
            
            $db = getDB();
            
            if ($action === 'create') {
                $stmt = $db->prepare("
                    INSERT INTO blog_posts (title, slug, content, excerpt, category, tags, meta_title, meta_description, is_published) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                if ($stmt->execute([$title, $slug, $content, $excerpt, $category, $tags, $meta_title, $meta_description, $is_published])) {
                    setFlashMessage('success', 'Blog post created successfully');
                } else {
                    setFlashMessage('error', 'Failed to create blog post');
                }
            } else {
                $post_id = intval($_POST['post_id']);
                $stmt = $db->prepare("
                    UPDATE blog_posts 
                    SET title = ?, slug = ?, content = ?, excerpt = ?, category = ?, tags = ?, 
                        meta_title = ?, meta_description = ?, is_published = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                
                if ($stmt->execute([$title, $slug, $content, $excerpt, $category, $tags, $meta_title, $meta_description, $is_published, $post_id])) {
                    setFlashMessage('success', 'Blog post updated successfully');
                } else {
                    setFlashMessage('error', 'Failed to update blog post');
                }
            }
        } elseif ($action === 'delete') {
            $post_id = intval($_POST['post_id']);
            $db = getDB();
            $stmt = $db->prepare("DELETE FROM blog_posts WHERE id = ?");
            
            if ($stmt->execute([$post_id])) {
                setFlashMessage('success', 'Blog post deleted successfully');
            } else {
                setFlashMessage('error', 'Failed to delete blog post');
            }
        }
    }
    
    redirect('blog.php');
}

// Get all blog posts
$db = getDB();
$stmt = $db->query("SELECT * FROM blog_posts ORDER BY created_at DESC");
$blog_posts = $stmt->fetchAll();

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Blog Management</h1>
                <p class="text-gray-600 mt-2">Create and manage blog posts</p>
            </div>
            <div class="flex space-x-4">
                <button onclick="openCreateModal()" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-plus mr-2"></i>New Post
                </button>
                <a href="index.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Admin
                </a>
            </div>
        </div>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-newspaper text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Posts</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo count($blog_posts); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-eye text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Published</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo count(array_filter($blog_posts, function($p) { return $p['is_published']; })); ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-edit text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Drafts</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo count(array_filter($blog_posts, function($p) { return !$p['is_published']; })); ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tags text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Categories</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php 
                            $categories = array_unique(array_column($blog_posts, 'category'));
                            echo count($categories);
                            ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Blog Posts Table -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">All Blog Posts</h2>
            </div>
            <div class="p-6">
                <?php if (empty($blog_posts)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-newspaper text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-600 mb-4">No blog posts yet</p>
                        <button onclick="openCreateModal()" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                            Create First Post
                        </button>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Title</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Category</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Created</th>
                                    <th class="text-center py-3 px-4 font-semibold text-gray-700">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($blog_posts as $post): ?>
                                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                                        <td class="py-4 px-4">
                                            <div>
                                                <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($post['title']); ?></p>
                                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars(substr($post['excerpt'], 0, 100)) . '...'; ?></p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-semibold">
                                                <?php echo ucfirst($post['category'] ?? 'general'); ?>
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="px-2 py-1 rounded-full text-xs font-semibold <?php 
                                                echo $post['is_published'] ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; 
                                            ?>">
                                                <?php echo $post['is_published'] ? 'Published' : 'Draft'; ?>
                                            </span>
                                        </td>
                                        <td class="py-4 px-4">
                                            <span class="text-sm text-gray-600"><?php echo date('M j, Y', strtotime($post['created_at'])); ?></span>
                                        </td>
                                        <td class="py-4 px-4 text-center">
                                            <div class="flex justify-center space-x-2">
                                                <button onclick="editPost(<?php echo $post['id']; ?>)" 
                                                        class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors">
                                                    <i class="fas fa-edit mr-1"></i>Edit
                                                </button>
                                                <button onclick="deletePost(<?php echo $post['id']; ?>, '<?php echo htmlspecialchars($post['title']); ?>')" 
                                                        class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600 transition-colors">
                                                    <i class="fas fa-trash mr-1"></i>Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
function openCreateModal() {
    // This would open a modal or redirect to create page
    window.location.href = 'blog-create.php';
}

function editPost(postId) {
    window.location.href = 'blog-edit.php?id=' + postId;
}

function deletePost(postId, title) {
    if (confirm('Are you sure you want to delete "' + title + '"?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="post_id" value="${postId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include '../includes/footer.php'; ?>
