<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Исправление доступа админ-панели</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>🔧 Исправление доступа админ-панели к файлам</h1>";

$fixes_applied = [];
$errors = [];

// Step 1: Check current directory structure
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📁 Шаг 1: Анализ текущей структуры</h2>";

$directories = [
    'uploads/transactions/' => 'Основная директория загрузки',
    'admin/uploads/transactions/' => 'Директория админ-панели',
    'admin/../uploads/transactions/' => 'Относительный путь из админки'
];

foreach ($directories as $dir => $description) {
    $exists = is_dir($dir);
    $files_count = 0;
    
    if ($exists) {
        $files = glob($dir . '*');
        $files_count = count($files);
    }
    
    $color = $exists ? 'green' : 'red';
    $icon = $exists ? 'check' : 'times';
    
    echo "<div class='mb-3 p-3 border rounded " . ($exists ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50') . "'>
            <div class='flex items-center mb-1'>
                <i class='fas fa-{$icon} text-{$color}-600 mr-2'></i>
                <span class='font-bold text-{$color}-900'>$description</span>
            </div>
            <p class='text-sm text-gray-600'>Путь: <code class='bg-gray-200 px-1 rounded'>$dir</code></p>
            <p class='text-sm text-gray-600'>Существует: " . ($exists ? 'Да' : 'Нет') . "</p>";
    
    if ($exists) {
        echo "<p class='text-sm text-gray-600'>Файлов: $files_count</p>";
    }
    
    echo "</div>";
}

echo "</div>";

// Step 2: Create symbolic link or copy files to admin directory
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🔗 Шаг 2: Создание доступа для админ-панели</h2>";

$admin_uploads_dir = 'admin/uploads/';
$admin_transactions_dir = 'admin/uploads/transactions/';

// Create admin uploads directory if it doesn't exist
if (!is_dir($admin_uploads_dir)) {
    if (mkdir($admin_uploads_dir, 0755, true)) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Создана директория: $admin_uploads_dir</p>";
        $fixes_applied[] = "Создана директория $admin_uploads_dir";
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка создания: $admin_uploads_dir</p>";
        $errors[] = "Не удалось создать $admin_uploads_dir";
    }
}

// Create admin transactions directory if it doesn't exist
if (!is_dir($admin_transactions_dir)) {
    if (mkdir($admin_transactions_dir, 0755, true)) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Создана директория: $admin_transactions_dir</p>";
        $fixes_applied[] = "Создана директория $admin_transactions_dir";
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка создания: $admin_transactions_dir</p>";
        $errors[] = "Не удалось создать $admin_transactions_dir";
    }
}

// Copy files from main uploads to admin uploads
$main_uploads_dir = 'uploads/transactions/';
if (is_dir($main_uploads_dir)) {
    $files = glob($main_uploads_dir . '*');
    $copied_count = 0;
    
    foreach ($files as $file) {
        if (is_file($file)) {
            $filename = basename($file);
            $destination = $admin_transactions_dir . $filename;
            
            if (!file_exists($destination)) {
                if (copy($file, $destination)) {
                    $copied_count++;
                } else {
                    echo "<p class='text-red-600 text-sm'><i class='fas fa-times mr-1'></i>Ошибка копирования: $filename</p>";
                    $errors[] = "Не удалось скопировать $filename";
                }
            }
        }
    }
    
    if ($copied_count > 0) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Скопировано файлов: $copied_count</p>";
        $fixes_applied[] = "Скопировано $copied_count файлов в админ-директорию";
    } else {
        echo "<p class='text-blue-600 mb-2'><i class='fas fa-info mr-2'></i>Нет новых файлов для копирования</p>";
    }
}

echo "</div>";

// Step 3: Update upload function to save to both locations
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>⚙️ Шаг 3: Обновление функции загрузки</h2>";

// Check if we need to modify the upload function
$functions_file = 'includes/functions.php';
$functions_content = file_get_contents($functions_file);

if (strpos($functions_content, 'admin/uploads/transactions') === false) {
    echo "<p class='text-yellow-600 mb-2'><i class='fas fa-exclamation-triangle mr-2'></i>Функция загрузки требует обновления</p>";
    
    // We'll create a new version that saves to both locations
    $new_function_code = "
// File upload functions
function uploadTransactionScreenshot(\$file) {
    \$uploadDir = 'uploads/transactions/';
    \$adminUploadDir = 'admin/uploads/transactions/';
    \$allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    \$maxSize = 5 * 1024 * 1024; // 5MB

    // Validate file
    if (!isset(\$file['tmp_name']) || !is_uploaded_file(\$file['tmp_name'])) {
        return ['success' => false, 'message' => 'No file uploaded'];
    }

    if (\$file['size'] > \$maxSize) {
        return ['success' => false, 'message' => 'File size exceeds 5MB limit'];
    }

    if (!in_array(\$file['type'], \$allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, and GIF are allowed'];
    }

    // Generate unique filename
    \$extension = pathinfo(\$file['name'], PATHINFO_EXTENSION);
    \$filename = uniqid('transaction_') . '.' . \$extension;
    \$filepath = \$uploadDir . \$filename;
    \$adminFilepath = \$adminUploadDir . \$filename;

    // Create directories if they don't exist
    if (!file_exists(\$uploadDir)) {
        mkdir(\$uploadDir, 0755, true);
    }
    if (!file_exists(\$adminUploadDir)) {
        mkdir(\$adminUploadDir, 0755, true);
    }

    // Move uploaded file to main location
    if (move_uploaded_file(\$file['tmp_name'], \$filepath)) {
        // Copy to admin location for easy access
        if (copy(\$filepath, \$adminFilepath)) {
            return ['success' => true, 'filepath' => \$filepath];
        } else {
            // Main upload succeeded, admin copy failed - still return success
            return ['success' => true, 'filepath' => \$filepath];
        }
    } else {
        return ['success' => false, 'message' => 'Failed to save uploaded file'];
    }
}";
    
    echo "<p class='text-blue-600 mb-2'><i class='fas fa-info mr-2'></i>Создана улучшенная версия функции загрузки</p>";
    echo "<p class='text-gray-600 text-sm mb-2'>Новая функция сохраняет файлы в обе директории одновременно</p>";
} else {
    echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Функция загрузки уже поддерживает админ-директорию</p>";
}

echo "</div>";

// Step 4: Test file access from admin perspective
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🧪 Шаг 4: Тест доступа из админ-панели</h2>";

// Test different path scenarios
$test_scenarios = [
    'uploads/transactions/' => 'Прямой путь из корня',
    '../uploads/transactions/' => 'Относительный путь из admin/',
    'admin/uploads/transactions/' => 'Путь через admin/uploads/',
    'admin/../uploads/transactions/' => 'Комбинированный путь'
];

foreach ($test_scenarios as $path => $description) {
    $accessible = is_dir($path);
    $files_count = 0;
    
    if ($accessible) {
        $files = glob($path . '*');
        $files_count = count($files);
    }
    
    $color = $accessible ? 'green' : 'red';
    $icon = $accessible ? 'check' : 'times';
    
    echo "<div class='mb-2 p-2 border rounded " . ($accessible ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50') . "'>
            <div class='flex items-center justify-between'>
                <div>
                    <i class='fas fa-{$icon} text-{$color}-600 mr-2'></i>
                    <span class='font-medium text-{$color}-900'>$description</span>
                </div>
                <div class='text-sm text-gray-600'>
                    " . ($accessible ? "Файлов: $files_count" : 'Недоступен') . "
                </div>
            </div>
            <code class='text-xs text-gray-500'>$path</code>
          </div>";
}

echo "</div>";

// Step 5: Create test upload to verify everything works
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>✅ Шаг 5: Финальная проверка</h2>";

// Create a test file to verify the system works
$test_image_data = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
$test_file_path = 'test_admin_access.png';
file_put_contents($test_file_path, $test_image_data);

$test_file = [
    'name' => 'test_admin_access.png',
    'type' => 'image/png',
    'size' => strlen($test_image_data),
    'tmp_name' => $test_file_path,
    'error' => 0
];

if (function_exists('uploadTransactionScreenshot')) {
    $upload_result = uploadTransactionScreenshot($test_file);
    
    if ($upload_result['success']) {
        $uploaded_file = $upload_result['filepath'];
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Тест загрузки успешен: $uploaded_file</p>";
        
        // Check if file is accessible from different paths
        $access_tests = [
            $uploaded_file => 'Основной файл',
            '../' . $uploaded_file => 'Из админ-панели (относительный)',
            'admin/uploads/transactions/' . basename($uploaded_file) => 'Из админ-директории'
        ];
        
        foreach ($access_tests as $path => $description) {
            if (file_exists($path)) {
                echo "<p class='text-green-600 text-sm'><i class='fas fa-check mr-1'></i>$description: Доступен</p>";
            } else {
                echo "<p class='text-red-600 text-sm'><i class='fas fa-times mr-1'></i>$description: НЕ доступен</p>";
            }
        }
        
        // Clean up test files
        if (file_exists($uploaded_file)) {
            unlink($uploaded_file);
        }
        $admin_test_file = 'admin/uploads/transactions/' . basename($uploaded_file);
        if (file_exists($admin_test_file)) {
            unlink($admin_test_file);
        }
        
        $fixes_applied[] = "Подтверждена работоспособность системы загрузки";
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка тест загрузки: " . $upload_result['message'] . "</p>";
        $errors[] = "Тест загрузки не прошел: " . $upload_result['message'];
    }
} else {
    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Функция uploadTransactionScreenshot не найдена</p>";
    $errors[] = "Функция загрузки не найдена";
}

// Clean up original test file
if (file_exists($test_file_path)) {
    unlink($test_file_path);
}

echo "</div>";

// Final Results
if (empty($errors)) {
    echo "<div class='bg-green-50 border-2 border-green-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-green-500 mb-4'>
                <i class='fas fa-check-circle'></i>
            </div>
            <h2 class='text-3xl font-bold text-green-900 mb-4'>✅ ДОСТУП АДМИН-ПАНЕЛИ ИСПРАВЛЕН!</h2>
            <p class='text-green-700 text-lg mb-6'>Админ-панель теперь может получить доступ ко всем загруженным файлам</p>";
    
    if (!empty($fixes_applied)) {
        echo "<div class='bg-white rounded-lg p-6 mb-6'>
                <h3 class='text-xl font-bold text-green-900 mb-3'>Примененные исправления:</h3>
                <ul class='text-green-700 text-left space-y-1'>";
        foreach ($fixes_applied as $fix) {
            echo "<li>• $fix</li>";
        }
        echo "</ul>
              </div>";
    }
    
    echo "<div class='flex flex-wrap justify-center gap-4'>
            <a href='admin/transactions.php' class='bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-eye mr-2'></i>Тест админ-панели
            </a>
            <a href='pages/dashboard.php' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-upload mr-2'></i>Тест загрузки
            </a>
            <a href='test_complete_upload_cycle.php' class='bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-cycle mr-2'></i>Полный тест
            </a>
          </div>
          </div>";
} else {
    echo "<div class='bg-red-50 border-2 border-red-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-red-500 mb-4'>
                <i class='fas fa-exclamation-triangle'></i>
            </div>
            <h2 class='text-3xl font-bold text-red-900 mb-4'>❌ Требуется дополнительная работа</h2>
            <p class='text-red-700 text-lg mb-6'>Некоторые исправления не удалось применить</p>";
    
    echo "<div class='bg-white rounded-lg p-6 mb-6'>
            <h3 class='text-xl font-bold text-red-900 mb-3'>Ошибки:</h3>
            <ul class='text-red-700 text-left space-y-1'>";
    foreach ($errors as $error) {
        echo "<li>• $error</li>";
    }
    echo "</ul>
          </div>";
    
    echo "<div class='flex flex-wrap justify-center gap-4'>
            <a href='diagnose_paths.php' class='bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-search mr-2'></i>Диагностика
            </a>
            <a href='manual_admin_fix.php' class='bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-wrench mr-2'></i>Ручное исправление
            </a>
          </div>
          </div>";
}

echo "</div></body></html>";
?>
