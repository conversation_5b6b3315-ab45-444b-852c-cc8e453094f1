<?php
// Test file for authentication system
require_once 'config/config.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Start session
session_start();

echo "<h1>Тест системы аутентификации</h1>";

// Test database connection
try {
    $db = getDB();
    echo "<p style='color: green;'>✅ Подключение к базе данных: УСПЕШНО</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Ошибка подключения к базе данных: " . $e->getMessage() . "</p>";
    exit;
}

// Test functions existence
$functions_to_test = [
    'registerUser',
    'loginUser',
    'setFlashMessage',
    'getFlashMessage',
    'getDashboardStats',
    'getUserInvestments',
    'getUserTransactions',
    'processDepositRequest',
    'processWithdrawalRequest'
];

echo "<h2>Проверка функций:</h2>";
foreach ($functions_to_test as $function) {
    if (function_exists($function)) {
        echo "<p style='color: green;'>✅ Функция {$function}: НАЙДЕНА</p>";
    } else {
        echo "<p style='color: red;'>❌ Функция {$function}: НЕ НАЙДЕНА</p>";
    }
}

// Test flash messages
setFlashMessage('success', 'Тестовое сообщение об успехе');
setFlashMessage('error', 'Тестовое сообщение об ошибке');

$success_msg = getFlashMessage('success');
$error_msg = getFlashMessage('error');

echo "<h2>Тест flash-сообщений:</h2>";
echo "<p style='color: green;'>Успех: " . ($success_msg ?: 'НЕ ПОЛУЧЕНО') . "</p>";
echo "<p style='color: red;'>Ошибка: " . ($error_msg ?: 'НЕ ПОЛУЧЕНО') . "</p>";

// Test CSRF token
echo "<h2>Тест CSRF токена:</h2>";
$token = generateCSRFToken();
echo "<p>Сгенерированный токен: " . substr($token, 0, 20) . "...</p>";
echo "<p style='color: " . (validateCSRFToken($token) ? 'green' : 'red') . "'>Валидация: " . (validateCSRFToken($token) ? 'УСПЕШНО' : 'ОШИБКА') . "</p>";

echo "<h2>Статус сессии:</h2>";
echo "<p>ID сессии: " . session_id() . "</p>";
echo "<p>Пользователь авторизован: " . (isLoggedIn() ? 'ДА' : 'НЕТ') . "</p>";
if (isLoggedIn()) {
    echo "<p>ID пользователя: " . getCurrentUserId() . "</p>";
    echo "<p>Имя пользователя: " . ($_SESSION['username'] ?? 'НЕ УСТАНОВЛЕНО') . "</p>";
    echo "<p>Администратор: " . (isAdmin() ? 'ДА' : 'НЕТ') . "</p>";
}

// Test dashboard functions if user is logged in
if (isLoggedIn()) {
    echo "<h2>Тест функций дашборда:</h2>";
    try {
        $user_id = getCurrentUserId();
        $stats = getDashboardStats($user_id);
        echo "<p style='color: green;'>✅ getDashboardStats: РАБОТАЕТ</p>";
        echo "<p>Баланс: " . formatCurrency($stats['balance']) . "</p>";
        echo "<p>Всего инвестировано: " . formatCurrency($stats['total_invested']) . "</p>";
        echo "<p>Общая прибыль: " . formatCurrency($stats['total_profit']) . "</p>";
        echo "<p>Активные инвестиции: " . $stats['active_investments'] . "</p>";

        $transactions = getUserTransactions($user_id, 5);
        echo "<p style='color: green;'>✅ getUserTransactions: РАБОТАЕТ (" . count($transactions) . " транзакций)</p>";

        $investments = getUserInvestments($user_id);
        echo "<p style='color: green;'>✅ getUserInvestments: РАБОТАЕТ (" . count($investments) . " инвестиций)</p>";

    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Ошибка в функциях дашборда: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<h2>Ссылки для тестирования:</h2>";
echo "<p><a href='pages/register.php'>Регистрация</a></p>";
echo "<p><a href='pages/login.php'>Вход</a></p>";
echo "<p><a href='pages/dashboard.php'>Дашборд</a></p>";
echo "<p><a href='pages/logout.php'>Выход</a></p>";
echo "<p><a href='admin/index.php'>Админка</a></p>";
?>
